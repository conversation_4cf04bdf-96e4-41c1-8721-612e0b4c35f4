#!/usr/bin/env python3
"""
Tests unitaires pour PatternAnalyzer.

Valide que chaque "détection" utilise des algorithmes programmés
et teste les calculs géométriques explicites.
"""

import sys
import os
sys.path.append('.')

import numpy as np
from models.pattern_analyzer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_rotation_detection():
    """Test de la détection de rotations par comparaison matricielle."""
    print("=== Test Détection de Rotations ===")
    
    analyzer = PatternAnalyzer()
    
    # Créer une grille de test simple
    input_grid = np.array([
        [1, 2, 0],
        [3, 4, 0],
        [0, 0, 0]
    ])
    
    # Test rotation 90°
    output_90 = np.rot90(input_grid, k=1)
    analysis = analyzer.analyze_transformations(input_grid, output_90)
    rotation = analysis['transformations']['rotation']
    
    assert rotation == 90, f"Rotation 90° non détectée: {rotation}"
    print("✓ Rotation 90° détectée par comparaison matricielle")
    
    # Test rotation 180°
    output_180 = np.rot90(input_grid, k=2)
    analysis = analyzer.analyze_transformations(input_grid, output_180)
    rotation = analysis['transformations']['rotation']
    
    assert rotation == 180, f"Rotation 180° non détectée: {rotation}"
    print("✓ Rotation 180° détectée par comparaison matricielle")
    
    # Test rotation 270°
    output_270 = np.rot90(input_grid, k=3)
    analysis = analyzer.analyze_transformations(input_grid, output_270)
    rotation = analysis['transformations']['rotation']
    
    assert rotation == 270, f"Rotation 270° non détectée: {rotation}"
    print("✓ Rotation 270° détectée par comparaison matricielle")
    
    # Test pas de rotation
    different_grid = np.array([
        [5, 6, 7],
        [8, 9, 1],
        [2, 3, 4]
    ])
    analysis = analyzer.analyze_transformations(input_grid, different_grid)
    rotation = analysis['transformations']['rotation']
    
    assert rotation is None, f"Fausse rotation détectée: {rotation}"
    print("✓ Absence de rotation correctement détectée")
    
    return True


def test_symmetry_detection():
    """Test des calculs de symétries avec des cas de test."""
    print("\n=== Test Détection de Symétries ===")
    
    analyzer = PatternAnalyzer()
    
    # Créer une grille de test
    input_grid = np.array([
        [1, 2, 3],
        [4, 5, 6],
        [7, 8, 9]
    ])
    
    # Test symétrie horizontale
    output_h = np.flipud(input_grid)
    analysis = analyzer.analyze_transformations(input_grid, output_h)
    symmetry = analysis['transformations']['symmetry']
    
    assert symmetry == "horizontal", f"Symétrie horizontale non détectée: {symmetry}"
    print("✓ Symétrie horizontale détectée par calculs d'axes")
    
    # Test symétrie verticale
    output_v = np.fliplr(input_grid)
    analysis = analyzer.analyze_transformations(input_grid, output_v)
    symmetry = analysis['transformations']['symmetry']
    
    assert symmetry == "vertical", f"Symétrie verticale non détectée: {symmetry}"
    print("✓ Symétrie verticale détectée par calculs d'axes")
    
    # Test symétrie diagonale (transposition)
    output_d = input_grid.T
    analysis = analyzer.analyze_transformations(input_grid, output_d)
    symmetry = analysis['transformations']['symmetry']
    
    assert symmetry == "diagonal", f"Symétrie diagonale non détectée: {symmetry}"
    print("✓ Symétrie diagonale détectée par transposition")
    
    return True


def test_color_analysis():
    """Test de l'analyse d'histogrammes de couleurs."""
    print("\n=== Test Analyse de Couleurs ===")
    
    analyzer = PatternAnalyzer()
    
    # Créer des grilles avec changements de couleurs
    input_grid = np.array([
        [1, 1, 2],
        [2, 2, 1],
        [1, 2, 1]
    ])
    
    # Changer couleur 1 -> 3 et couleur 2 -> 4
    output_grid = np.array([
        [3, 3, 4],
        [4, 4, 3],
        [3, 4, 3]
    ])
    
    analysis = analyzer.analyze_transformations(input_grid, output_grid)
    color_changes = analysis['transformations']['color_changes']
    
    # Vérifier que les changements sont détectés
    assert len(color_changes) > 0, "Aucun changement de couleur détecté"
    print(f"✓ Changements de couleurs détectés par histogrammes: {color_changes}")
    
    return True


def test_categorization_rules():
    """Test de la catégorisation par règles explicites."""
    print("\n=== Test Catégorisation par Règles ===")
    
    analyzer = PatternAnalyzer()
    
    # Test catégorie geometric_transform
    input_grid = np.array([[1, 2], [3, 4]])
    output_grid = np.rot90(input_grid)
    train_examples = [(input_grid, output_grid)]
    
    category = analyzer.categorize_puzzle(train_examples)
    assert category == "geometric_transform", f"Catégorie incorrecte: {category}"
    print("✓ Catégorie 'geometric_transform' assignée par règles conditionnelles")
    
    # Test catégorie color_pattern
    input_grid = np.array([[1, 1], [1, 1]])
    output_grid = np.array([[2, 2], [2, 2]])
    train_examples = [(input_grid, output_grid)]
    
    category = analyzer.categorize_puzzle(train_examples)
    assert category == "color_pattern", f"Catégorie incorrecte: {category}"
    print("✓ Catégorie 'color_pattern' assignée par règles conditionnelles")
    
    # Test catégorie resize_operation
    input_grid = np.array([[1, 2]])
    output_grid = np.array([[1, 2], [3, 4]])
    train_examples = [(input_grid, output_grid)]
    
    category = analyzer.categorize_puzzle(train_examples)
    assert category == "resize_operation", f"Catégorie incorrecte: {category}"
    print("✓ Catégorie 'resize_operation' assignée par règles conditionnelles")
    
    return True


def test_algorithmic_nature():
    """Valide que chaque 'détection' utilise des algorithmes programmés."""
    print("\n=== Test Nature Algorithmique ===")
    
    analyzer = PatternAnalyzer()
    
    # Vérifier les statistiques de l'analyseur
    stats = analyzer.get_analysis_stats()
    
    assert stats['method'] == 'algorithmic_pattern_detection', "Méthode incorrecte"
    assert 'pas d\'apprentissage automatique' in stats['note'], "Message de transparence manquant"
    print("✓ Méthode confirmée comme algorithmique, pas d'apprentissage")
    
    # Vérifier qu'une analyse retourne des métadonnées honnêtes
    input_grid = np.array([[1, 2], [3, 4]])
    output_grid = np.array([[4, 3], [2, 1]])
    
    analysis = analyzer.analyze_transformations(input_grid, output_grid)
    
    assert analysis['method'] == 'algorithmic_calculation', "Méthode d'analyse incorrecte"
    assert 'execution_time_ms' in analysis, "Temps d'exécution manquant"
    print("✓ Analyse retourne des métadonnées honnêtes sur la méthode")
    
    return True


def test_copy_paste_detection():
    """Test de détection de motifs répétés par corrélation."""
    print("\n=== Test Détection Copy-Paste ===")
    
    analyzer = PatternAnalyzer()
    
    # Créer une grille avec un motif répété
    input_grid = np.array([
        [1, 2, 0, 0],
        [3, 4, 0, 0],
        [0, 0, 0, 0],
        [0, 0, 0, 0]
    ])
    
    output_grid = np.array([
        [1, 2, 1, 2],
        [3, 4, 3, 4],
        [1, 2, 0, 0],
        [3, 4, 0, 0]
    ])
    
    analysis = analyzer.analyze_transformations(input_grid, output_grid)
    copy_paste = analysis['transformations']['copy_paste']
    
    assert copy_paste == True, "Motif répété non détecté"
    print("✓ Motifs répétés détectés par corrélation spatiale")
    
    return True


def test_fill_pattern_detection():
    """Test de détection d'opérations de remplissage."""
    print("\n=== Test Détection Fill Pattern ===")
    
    analyzer = PatternAnalyzer()
    
    # Créer une grille avec remplissage
    input_grid = np.array([
        [0, 0, 1],
        [0, 1, 0],
        [1, 0, 0]
    ])
    
    output_grid = np.array([
        [2, 2, 1],
        [2, 1, 2],
        [1, 2, 2]
    ])
    
    analysis = analyzer.analyze_transformations(input_grid, output_grid)
    fill_pattern = analysis['transformations']['fill_pattern']
    
    assert fill_pattern == True, "Remplissage non détecté"
    print("✓ Opération de remplissage détectée")
    
    return True


def test_performance_requirements():
    """Test des exigences de performance (< 2 secondes)."""
    print("\n=== Test Performance ===")
    
    analyzer = PatternAnalyzer()
    
    # Créer une grille plus grande pour tester la performance
    input_grid = np.random.randint(0, 10, (20, 20))
    output_grid = np.random.randint(0, 10, (20, 20))
    
    analysis = analyzer.analyze_transformations(input_grid, output_grid)
    execution_time = analysis['execution_time_ms']
    
    assert execution_time < 2000, f"Analyse trop lente: {execution_time}ms"
    print(f"✓ Analyse terminée en {execution_time:.2f}ms (< 2000ms)")
    
    return True


def main():
    """Fonction principale de test."""
    print("Tests unitaires PatternAnalyzer")
    print("=" * 50)
    
    success = True
    
    try:
        success &= test_rotation_detection()
        success &= test_symmetry_detection()
        success &= test_color_analysis()
        success &= test_categorization_rules()
        success &= test_algorithmic_nature()
        success &= test_copy_paste_detection()
        success &= test_fill_pattern_detection()
        success &= test_performance_requirements()
        
        if success:
            print("\n🎉 Tous les tests PatternAnalyzer sont passés!")
            print("Chaque 'détection' utilise des algorithmes programmés")
        else:
            print("\n❌ Certains tests ont échoué")
            
    except Exception as e:
        print(f"\n💥 Erreur durant les tests: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
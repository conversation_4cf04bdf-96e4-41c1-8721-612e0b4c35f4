# Design Document

## Overview

Le pipeline ARC-Solver implémente une architecture algorithmique à deux étages pour résoudre les puzzles ARC. Le système utilise des calculs géométriques explicites pour analyser les patterns, puis des algorithmes de recherche pour résoudre les paramètres. Cette approche privilégie la transparence technique et évite de prétendre à des capacités d'IA inexistantes.

## Architecture

### Architecture Générale

```
Input ARC Puzzle
       ↓
MultiExampleDataset (Chargement + Normalisation)
       ↓
PatternAnalyzer (Calculs géométriques)
       ↓
ScenarioGeneralizer (Templates pré-définis)
       ↓
HRMParameterResolver (Recherche algorithmique)
       ↓
CommandExecutor (Exécution AGI)
       ↓
Output Solution
```

### Composants Réutilisables Existants

- **GrammarTokenizer** : Parsing AGI fonctionnel (déjà implémenté)
- **ArcDataset** : Chargement des puzzles JSON (base existante)
- **GridToProgramHRM** : Architecture transformer de base (réutilisable)
- **CommandExecutor** : Exécution des commandes AGI (fonctionnel)

### Nouveaux Composants à Développer

1. **MultiExampleDataset** : Extension du dataset existant
2. **PatternAnalyzer** : Nouveau composant d'analyse géométrique
3. **ScenarioGeneralizer** : Nouveau générateur de templates
4. **HRMParameterResolver** : Nouveau résolveur algorithmique
5. **ARCSolverPipeline** : Orchestrateur principal

## Components and Interfaces

### 1. MultiExampleDataset

**Fichier :** `src/multi_example_dataset.py`

```python
class MultiExampleARCDataset(Dataset):
    """
    Dataset qui charge TOUS les exemples train d'un puzzle.
    Algorithme classique de parsing JSON, pas d'IA.
    """
    
    def __init__(self, data_dir: str, max_grid_size: int = 30):
        self.data_dir = data_dir
        self.max_grid_size = max_grid_size
        self.puzzles = self._load_puzzles()
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        Retourne:
        - train_inputs: List[torch.Tensor] - Toutes les grilles d'entrée
        - train_outputs: List[torch.Tensor] - Toutes les grilles de sortie
        - test_input: torch.Tensor - Grille de test
        - puzzle_id: str - Identifiant du puzzle
        """
    
    def _normalize_grid_size(self, grid: np.ndarray) -> torch.Tensor:
        """Padding des grilles à la taille maximale"""
    
    def _load_puzzles(self) -> List[Dict]:
        """Chargement JSON standard des puzzles"""
```

### 2. PatternAnalyzer

**Fichier :** `models/pattern_analyzer.py`

```python
class PatternAnalyzer:
    """
    Détection de patterns par calculs algorithmiques explicites.
    Chaque 'détection' est un algorithme programmé, pas de l'apprentissage.
    """
    
    def analyze_transformations(self, input_grid: np.ndarray, output_grid: np.ndarray) -> Dict[str, Any]:
        """
        Analyse par calculs géométriques :
        - Rotations : comparaison matricielle (rot90, rot180, rot270)
        - Symétries : calculs d'axes (horizontal, vertical, diagonal)
        - Couleurs : histogrammes et comparaisons
        - Motifs : corrélation spatiale
        """
        
    def categorize_puzzle(self, train_examples: List[Tuple[np.ndarray, np.ndarray]]) -> str:
        """
        Classification par règles explicites :
        - geometric_transform : si rotation/symétrie détectée
        - color_pattern : si changements de couleurs
        - resize_operation : si changement de taille
        - copy_paste : si motifs répétés
        - fill_pattern : si remplissage détecté
        """
    
    def _detect_rotation(self, input_grid: np.ndarray, output_grid: np.ndarray) -> Optional[int]:
        """Comparaison matricielle pour détecter rotations 90°, 180°, 270°"""
    
    def _detect_symmetry(self, input_grid: np.ndarray, output_grid: np.ndarray) -> Optional[str]:
        """Calculs d'axes de symétrie"""
    
    def _analyze_color_changes(self, input_grid: np.ndarray, output_grid: np.ndarray) -> Dict[int, int]:
        """Histogrammes de couleurs et mapping"""
```

### 3. ScenarioGeneralizer

**Fichier :** `src/scenario_generalizer.py`

```python
class ScenarioGeneralizer:
    """
    Génération de templates par substitution de variables.
    Base de données de patterns pré-définis, pas d'IA.
    """
    
    def __init__(self):
        self.templates = self._load_templates()
    
    def generalize_scenario(self, category: str, train_examples: List) -> Dict[str, Any]:
        """
        Algorithme de substitution :
        1. Sélectionner template selon catégorie
        2. Identifier valeurs variables
        3. Remplacer par placeholders
        4. Définir contraintes
        """
    
    def _load_templates(self) -> Dict[str, str]:
        """
        Templates pré-définis par catégorie :
        {
            "color_fill": "FILL {color_var} [0,0 {width},{height}]",
            "rotation": "ROTATE {angle} {center}",
            "copy_pattern": "COPY {region}; PASTE {positions}",
            "symmetry": "MIRROR {axis} {region}",
            "resize": "RESIZE {scale_factor} {method}"
        }
        """
    
    def _extract_variables(self, template: str, train_examples: List) -> Dict[str, List]:
        """Identification des valeurs variables dans les exemples"""
```

### 4. HRMParameterResolver

**Fichier :** `models/hrm_parameter_resolver.py`

```python
class HRMParameterResolver:
    """
    Résolution de paramètres par recherche algorithmique.
    Force brute optimisée, pas d'apprentissage automatique.
    """
    
    def __init__(self, timeout_seconds: int = 10):
        self.timeout_seconds = timeout_seconds
        self.command_executor = CommandExecutor()
    
    def resolve_parameters(self, template: str, test_input: np.ndarray, 
                          train_examples: List) -> Optional[str]:
        """
        Algorithme de résolution :
        1. Énumérer valeurs possibles pour chaque variable
        2. Tester combinaisons sur train examples
        3. Sélectionner meilleure combinaison (score)
        4. Appliquer au test_input
        """
    
    def validate_solution(self, scenario: str, train_examples: List) -> float:
        """
        Validation par exécution :
        - Exécuter scenario sur chaque train input
        - Comparer avec train output attendu
        - Calculer score de correspondance (0.0 à 1.0)
        """
    
    def _enumerate_parameter_values(self, template: str, train_examples: List) -> Dict[str, List]:
        """Énumération des valeurs possibles pour chaque paramètre"""
    
    def _test_parameter_combination(self, scenario: str, train_examples: List) -> bool:
        """Test d'une combinaison de paramètres sur tous les exemples"""
```

### 5. ARCSolverPipeline

**Fichier :** `models/arc_solver_pipeline.py`

```python
class ARCSolverPipeline:
    """
    Orchestration des composants algorithmiques.
    Pipeline explicite sans prétention d'intelligence.
    """
    
    def __init__(self):
        self.pattern_analyzer = PatternAnalyzer()
        self.scenario_generalizer = ScenarioGeneralizer()
        self.parameter_resolver = HRMParameterResolver()
    
    def solve_puzzle(self, puzzle_data: Dict) -> Dict[str, Any]:
        """
        Pipeline explicite :
        1. Analyser patterns (calculs géométriques)
        2. Catégoriser puzzle (règles conditionnelles)
        3. Générer template (substitution variables)
        4. Résoudre paramètres (recherche algorithmique)
        5. Exécuter solution (interpréteur AGI)
        """
    
    def _log_step(self, step_name: str, result: Any, execution_time: float):
        """Logging transparent de chaque étape"""
```

## Data Models

### PuzzleData
```python
@dataclass
class PuzzleData:
    puzzle_id: str
    train_inputs: List[np.ndarray]
    train_outputs: List[np.ndarray]
    test_input: np.ndarray
    expected_output: Optional[np.ndarray] = None
```

### AnalysisResult
```python
@dataclass
class AnalysisResult:
    category: str
    confidence: float
    detected_transformations: List[str]
    analysis_details: Dict[str, Any]
    execution_time_ms: float
```

### TemplateResult
```python
@dataclass
class TemplateResult:
    template: str
    variables: Dict[str, List]
    constraints: Dict[str, Any]
    generation_method: str  # "rule_based", pas "learned"
```

### SolutionResult
```python
@dataclass
class SolutionResult:
    success: bool
    solution_command: Optional[str]
    execution_time_ms: float
    validation_score: float
    error_message: Optional[str]
    method_used: str  # "algorithmic_search", pas "ai_inference"
```

## Error Handling

### Stratégie d'Erreurs Explicites

1. **Erreurs de Chargement** : JSON corrompu, fichiers manquants
2. **Erreurs d'Analyse** : Patterns non reconnus, calculs impossibles
3. **Erreurs de Templates** : Catégorie inconnue, template manquant
4. **Erreurs de Résolution** : Timeout, aucune solution trouvée
5. **Erreurs d'Exécution** : Commande AGI invalide, grille corrompue

### Gestion des Timeouts

- **Analyse de patterns** : Maximum 2 secondes
- **Résolution de paramètres** : Maximum 10 secondes
- **Pipeline complet** : Maximum 15 secondes

### Messages d'Erreur Honnêtes

```python
ERROR_MESSAGES = {
    "no_pattern_detected": "Aucun pattern géométrique détecté par les algorithmes programmés",
    "template_not_found": "Aucun template pré-défini pour cette catégorie",
    "parameter_search_failed": "La recherche algorithmique n'a trouvé aucune solution valide",
    "timeout_exceeded": "Timeout dépassé lors de la recherche par force brute",
    "execution_failed": "Échec de l'exécution de la commande AGI générée"
}
```

## Testing Strategy

### Tests Unitaires

1. **test_multi_example_dataset.py**
   - Chargement de tous les exemples
   - Normalisation des grilles
   - Gestion des erreurs JSON

2. **test_pattern_analyzer.py**
   - Détection de rotations (comparaison matricielle)
   - Calculs de symétries
   - Analyse d'histogrammes de couleurs
   - Catégorisation par règles

3. **test_scenario_generalizer.py**
   - Sélection de templates
   - Substitution de variables
   - Génération de contraintes

4. **test_parameter_resolver.py**
   - Énumération de paramètres
   - Validation sur exemples train
   - Gestion des timeouts

5. **test_pipeline_integration.py**
   - Pipeline complet
   - Gestion d'erreurs entre composants
   - Métriques de performance

### Tests d'Intégration

1. **Puzzles Simples** : Tests sur 10 puzzles de base
2. **Puzzles de Rotation** : Validation des calculs géométriques
3. **Puzzles de Couleurs** : Test des histogrammes
4. **Puzzles Complexes** : Limites du système

### Métriques de Test

- **Précision de catégorisation** : % de puzzles correctement classés
- **Taux de résolution** : % de paramètres trouvés
- **Temps d'exécution** : Performance algorithmique
- **Couverture des templates** : % de puzzles couverts

## Performance Considerations

### Optimisations Algorithmiques

1. **Cache des calculs** : Éviter les recalculs de patterns
2. **Recherche guidée** : Priorité aux paramètres les plus probables
3. **Parallélisation** : Tests de paramètres en parallèle
4. **Early stopping** : Arrêt dès qu'une solution parfaite est trouvée

### Limitations de Performance

- **Complexité exponentielle** : Recherche de paramètres O(n^k)
- **Mémoire** : Stockage de tous les exemples train
- **Timeout nécessaire** : Éviter les boucles infinies

### Métriques de Performance Cibles

- **30% de puzzles résolus** sur un sous-ensemble simple
- **Temps < 10s** par puzzle
- **Mémoire < 1GB** pour le dataset complet
- **100% de transparence** sur les méthodes utilisées
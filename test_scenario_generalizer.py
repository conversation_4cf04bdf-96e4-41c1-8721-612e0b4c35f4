#!/usr/bin/env python3
"""
Tests unitaires pour ScenarioGeneralizer.

Valide la sélection de templates selon les catégories,
la substitution de variables avec placeholders,
et la génération de contraintes de résolution.
"""

import sys
import os
sys.path.append('.')

import numpy as np
from src.scenario_generalizer import ScenarioGeneralizer


def test_template_selection():
    """Test de la sélection de templates selon les catégories."""
    print("=== Test Sélection de Templates ===")
    
    generalizer = ScenarioGeneralizer()
    
    # Test catégories connues
    test_cases = [
        ('color_pattern', 'REPLACE'),
        ('geometric_transform', 'ROTATE'),
        ('resize_operation', 'RESIZE'),
        ('fill_pattern', 'FILL'),
        ('copy_paste', 'COPY')
    ]
    
    train_examples = [
        (np.array([[1, 2], [3, 4]]), np.array([[5, 6], [7, 8]]))
    ]
    
    for category, expected_command in test_cases:
        scenario = generalizer.generalize_scenario(category, train_examples)
        template = scenario['template']
        
        assert expected_command in template.upper(), f"Template {category} doit contenir {expected_command}"
        print(f"✓ {category}: {template}")
    
    # Test catégorie inconnue
    scenario = generalizer.generalize_scenario('unknown_category', train_examples)
    assert scenario['category'] == 'complex_pattern', "Catégorie inconnue doit utiliser le template par défaut"
    print("✓ Catégorie inconnue gérée correctement")
    
    return True


def test_variable_substitution():
    """Test de la substitution de variables avec placeholders."""
    print("\n=== Test Substitution de Variables ===")
    
    generalizer = ScenarioGeneralizer()
    
    train_examples = [
        (np.array([[1, 2, 0], [3, 4, 0], [0, 0, 0]]), 
         np.array([[5, 6, 0], [7, 8, 0], [0, 0, 0]]))
    ]
    
    scenario = generalizer.generalize_scenario('color_pattern', train_examples)
    
    # Vérifier que le template contient des placeholders
    template = scenario['template']
    assert '{' in template and '}' in template, "Template doit contenir des placeholders"
    print(f"✓ Template avec placeholders: {template}")
    
    # Vérifier que les variables sont extraites
    variables = scenario['variables']
    assert isinstance(variables, dict), "Variables doivent être un dictionnaire"
    assert len(variables) > 0, "Doit avoir des variables extraites"
    print(f"✓ Variables extraites: {list(variables.keys())}")
    
    return True


def test_constraint_generation():
    """Test de la génération de contraintes de résolution."""
    print("\n=== Test Génération de Contraintes ===")
    
    generalizer = ScenarioGeneralizer()
    
    train_examples = [
        (np.array([[1, 2], [3, 4]]), np.array([[5, 6], [7, 8]])),
        (np.array([[2, 3], [4, 5]]), np.array([[6, 7], [8, 9]]))
    ]
    
    scenario = generalizer.generalize_scenario('color_pattern', train_examples)
    
    # Vérifier les contraintes
    constraints = scenario['constraints']
    assert isinstance(constraints, dict), "Contraintes doivent être un dictionnaire"
    
    for var_name, constraint in constraints.items():
        assert 'type' in constraint, f"Contrainte {var_name} doit avoir un type"
        assert 'method' in constraint, f"Contrainte {var_name} doit avoir une méthode"
        assert 'possible_values' in constraint, f"Contrainte {var_name} doit avoir des valeurs possibles"
        print(f"✓ Contrainte {var_name}: {constraint['type']} par {constraint['method']}")
    
    return True


def test_color_extraction():
    """Test de l'extraction de couleurs des exemples."""
    print("\n=== Test Extraction de Couleurs ===")
    
    generalizer = ScenarioGeneralizer()
    
    train_examples = [
        (np.array([[1, 2, 3], [4, 5, 6]]), np.array([[7, 8, 9], [1, 2, 3]]))
    ]
    
    colors = generalizer._extract_color_values(train_examples)
    
    expected_colors = [1, 2, 3, 4, 5, 6, 7, 8, 9]
    assert set(colors) == set(expected_colors), f"Couleurs extraites incorrectes: {colors}"
    print(f"✓ Couleurs extraites: {colors}")
    
    return True


def test_size_extraction():
    """Test de l'extraction de tailles des exemples."""
    print("\n=== Test Extraction de Tailles ===")
    
    generalizer = ScenarioGeneralizer()
    
    train_examples = [
        (np.array([[1, 2], [3, 4]]), np.array([[5, 6, 7], [8, 9, 1], [2, 3, 4]]))
    ]
    
    sizes = generalizer._extract_size_values(train_examples)
    
    # Vérifier que les tailles des grilles sont extraites
    assert '2' in sizes, "Largeur input doit être extraite"
    assert '3' in sizes, "Largeur output doit être extraite"
    assert '2x2' in sizes, "Taille input complète doit être extraite"
    assert '3x3' in sizes, "Taille output complète doit être extraite"
    print(f"✓ Tailles extraites: {sizes}")
    
    return True


def test_template_validation():
    """Test de validation des templates."""
    print("\n=== Test Validation de Templates ===")
    
    generalizer = ScenarioGeneralizer()
    
    # Template valide
    valid_template = "FILL {color_var} [0,0 {width},{height}]"
    validation = generalizer.validate_template(valid_template)
    
    assert validation['is_valid'], f"Template valide doit passer: {validation['errors']}"
    print("✓ Template valide accepté")
    
    # Template invalide (syntaxe)
    invalid_template = "INVALID_COMMAND {var}"
    validation = generalizer.validate_template(invalid_template)
    
    assert not validation['is_valid'], "Template invalide doit être rejeté"
    print("✓ Template invalide rejeté")
    
    # Template avec accolades non équilibrées
    unbalanced_template = "FILL {color_var [0,0]"
    validation = generalizer.validate_template(unbalanced_template)
    
    assert not validation['is_valid'], "Template avec accolades non équilibrées doit être rejeté"
    print("✓ Accolades non équilibrées détectées")
    
    return True


def test_rule_based_method():
    """Test que la méthode est bien rule-based et pas learned."""
    print("\n=== Test Méthode Rule-Based ===")
    
    generalizer = ScenarioGeneralizer()
    
    train_examples = [
        (np.array([[1, 2], [3, 4]]), np.array([[5, 6], [7, 8]]))
    ]
    
    scenario = generalizer.generalize_scenario('color_pattern', train_examples)
    
    # Vérifier que la méthode est rule-based
    assert scenario['generation_method'] == 'rule_based', "Méthode doit être rule-based"
    print("✓ Méthode confirmée comme rule-based, pas learned")
    
    # Vérifier les statistiques du généralisateur
    stats = generalizer.get_template_stats()
    assert 'rule_based' in stats['method'], "Statistiques doivent indiquer rule-based"
    assert 'pas d\'apprentissage automatique' in stats['note'], "Doit préciser que ce n'est pas de l'apprentissage automatique"
    print("✓ Statistiques confirment l'approche algorithmique")
    
    return True


def test_all_categories():
    """Test de génération pour toutes les catégories disponibles."""
    print("\n=== Test Toutes les Catégories ===")
    
    generalizer = ScenarioGeneralizer()
    categories = generalizer.get_available_categories()
    
    train_examples = [
        (np.array([[1, 2], [3, 4]]), np.array([[5, 6], [7, 8]]))
    ]
    
    for category in categories:
        scenario = generalizer.generalize_scenario(category, train_examples)
        
        # Vérifications de base
        assert scenario['category'] == category, f"Catégorie incorrecte pour {category}"
        assert isinstance(scenario['template'], str), f"Template doit être une string pour {category}"
        assert isinstance(scenario['variables'], dict), f"Variables doivent être un dict pour {category}"
        assert isinstance(scenario['constraints'], dict), f"Contraintes doivent être un dict pour {category}"
        
        print(f"✓ {category}: {scenario['template'][:30]}...")
    
    print(f"✓ {len(categories)} catégories testées")
    
    return True


def test_requirements_compliance():
    """Vérifie la conformité aux requirements."""
    print("\n=== Vérification des Requirements ===")
    
    generalizer = ScenarioGeneralizer()
    
    train_examples = [
        (np.array([[1, 2], [3, 4]]), np.array([[5, 6], [7, 8]]))
    ]
    
    # Requirement 3.1: Sélectionner template selon catégorie
    scenario = generalizer.generalize_scenario('color_pattern', train_examples)
    assert 'REPLACE' in scenario['template'], "Template doit correspondre à la catégorie"
    print("✓ Requirement 3.1: Sélection de template selon catégorie")
    
    # Requirement 3.2: Identifier valeurs variables
    variables = scenario['variables']
    assert len(variables) > 0, "Doit identifier des valeurs variables"
    print("✓ Requirement 3.2: Identification des valeurs variables")
    
    # Requirement 3.3: Remplacer par placeholders
    template = scenario['template']
    assert '{' in template and '}' in template, "Template doit contenir des placeholders"
    print("✓ Requirement 3.3: Remplacement par placeholders")
    
    # Requirement 3.4: Définir contraintes de résolution
    constraints = scenario['constraints']
    assert len(constraints) > 0, "Doit définir des contraintes de résolution"
    print("✓ Requirement 3.4: Définition des contraintes de résolution")
    
    # Requirement 3.5 & 3.6: Templates spécifiques
    color_fill_scenario = generalizer.generalize_scenario('color_fill', train_examples)
    assert 'FILL' in color_fill_scenario['template'], "Template color_fill doit contenir FILL"
    
    rotation_scenario = generalizer.generalize_scenario('rotation', train_examples)
    assert 'ROTATE' in rotation_scenario['template'], "Template rotation doit contenir ROTATE"
    
    print("✓ Requirements 3.5 & 3.6: Templates spécifiques corrects")
    
    return True


def main():
    """Fonction principale de test."""
    print("Tests unitaires ScenarioGeneralizer")
    print("=" * 50)
    
    success = True
    
    try:
        success &= test_template_selection()
        success &= test_variable_substitution()
        success &= test_constraint_generation()
        success &= test_color_extraction()
        success &= test_size_extraction()
        success &= test_template_validation()
        success &= test_rule_based_method()
        success &= test_all_categories()
        success &= test_requirements_compliance()
        
        if success:
            print("\n🎉 Tous les tests ScenarioGeneralizer sont passés!")
            print("Génération de templates par substitution de variables validée")
        else:
            print("\n❌ Certains tests ont échoué")
            
    except Exception as e:
        print(f"\n💥 Erreur durant les tests: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
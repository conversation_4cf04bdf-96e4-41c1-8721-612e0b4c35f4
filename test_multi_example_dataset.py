#!/usr/bin/env python3
"""
Script de test pour le MultiExampleDataset.

Valide que le dataset charge correctement tous les exemples train
et normalise les grilles comme spécifié.
"""

import sys
import os
sys.path.append('.')

from src.multi_example_dataset import MultiExampleARCDataset, MultiExampleDatasetWithPrograms
from src.tokenizer import GrammarTokenizer


def test_basic_functionality():
    """Test des fonctionnalités de base du MultiExampleARCDataset."""
    print("=== Test MultiExampleARCDataset ===")
    
    # Créer le dataset
    dataset = MultiExampleARCDataset('arcdata/training')
    
    print(f"Nombre de puzzles chargés: {len(dataset)}")
    
    if len(dataset) == 0:
        print("ERREUR: Aucun puzzle chargé!")
        return False
    
    # Tester le premier élément
    sample = dataset[0]
    
    print(f"Puzzle ID: {sample['puzzle_id']}")
    print(f"Nombre d'exemples train: {len(sample['train_inputs'])}")
    print(f"Forme test_input: {sample['test_input'].shape}")
    
    # Vérifier les formes des grilles d'entrée
    for i, input_grid in enumerate(sample['train_inputs']):
        print(f"Train {i+1} - Input: {input_grid.shape}")
        # Vérifier que les grilles sont bien normalisées à 30x30
        assert input_grid.shape == (30, 30), f"Input {i} mal normalisé: {input_grid.shape}"
    
    # Vérifier les formes des grilles de sortie
    for i, output_grid in enumerate(sample['train_outputs']):
        print(f"Train {i+1} - Output: {output_grid.shape}")
        assert output_grid.shape == (30, 30), f"Output {i} mal normalisé: {output_grid.shape}"
    
    assert sample['test_input'].shape == (30, 30), f"Test input mal normalisé: {sample['test_input'].shape}"
    
    print("✓ Normalisation des grilles OK")
    
    # Afficher les statistiques
    stats = dataset.get_puzzle_stats()
    print("\n=== Statistiques du dataset ===")
    for key, value in stats.items():
        print(f"{key}: {value}")
    
    return True


def test_with_programs():
    """Test du MultiExampleDatasetWithPrograms."""
    print("\n=== Test MultiExampleDatasetWithPrograms ===")
    
    # Créer le tokenizer
    try:
        tokenizer = GrammarTokenizer()
    except Exception as e:
        print(f"ATTENTION: Erreur création tokenizer: {e}, test avec tokenizer None")
        tokenizer = None
    
    # Créer le dataset avec programmes
    if tokenizer:
        dataset = MultiExampleDatasetWithPrograms('arcdata/training', tokenizer)
    else:
        print("Tokenizer non disponible, test ignoré")
        return True
    
    if len(dataset) == 0:
        print("ERREUR: Aucun puzzle chargé!")
        return False
    
    # Tester un échantillon
    sample = dataset[0]
    
    print(f"Puzzle ID: {sample['puzzle_id']}")
    print(f"Programme disponible: {sample['program_text'] is not None}")
    
    if sample['program_tokens'] is not None:
        print(f"Tokens programme: {sample['program_tokens'].shape}")
        print(f"Programme: {sample['program_text'][:100]}...")
    
    return True


def test_json_error_handling():
    """Test de la gestion des erreurs JSON."""
    print("\n=== Test Gestion Erreurs JSON ===")
    
    # Créer un dossier temporaire avec un fichier JSON corrompu
    import tempfile
    import json
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Créer un fichier JSON valide
        valid_puzzle = {
            "train": [{"input": [[1, 0], [0, 1]], "output": [[0, 1], [1, 0]]}],
            "test": [{"input": [[1, 1], [0, 0]]}]
        }
        
        valid_path = os.path.join(temp_dir, "valid.json")
        with open(valid_path, 'w') as f:
            json.dump(valid_puzzle, f)
        
        # Créer un fichier JSON corrompu
        corrupt_path = os.path.join(temp_dir, "corrupt.json")
        with open(corrupt_path, 'w') as f:
            f.write("{ invalid json content")
        
        # Tester que les fichiers corrompus sont ignorés (pas d'exception levée)
        try:
            dataset = MultiExampleARCDataset(temp_dir)
            # Le dataset devrait contenir seulement le fichier valide
            if len(dataset) == 1:
                print("✓ Requirement 1.4: Fichiers JSON corrompus ignorés correctement")
                return True
            else:
                print(f"ERREUR: Dataset devrait contenir 1 puzzle, trouvé {len(dataset)}")
                return False
        except Exception as e:
            print(f"ERREUR: Exception inattendue: {e}")
            return False


def test_requirements_compliance():
    """Vérifie la conformité aux requirements."""
    print("\n=== Vérification des Requirements ===")
    
    dataset = MultiExampleARCDataset('arcdata/training')
    
    if len(dataset) == 0:
        print("ERREUR: Pas de données pour tester")
        return False
    
    sample = dataset[0]
    
    # Requirement 1.1: Retourner TOUS les exemples train (pas seulement le premier)
    assert len(sample['train_inputs']) >= 1, "Doit avoir au moins 1 exemple train"
    assert len(sample['train_inputs']) == len(sample['train_outputs']), "Nombre inputs != outputs"
    print(f"✓ Requirement 1.1: {len(sample['train_inputs'])} exemples train chargés")
    
    # Requirement 1.2: Normalisation avec padding à 30x30
    for input_grid in sample['train_inputs']:
        assert input_grid.shape == (30, 30), "Input doit être 30x30"
    for output_grid in sample['train_outputs']:
        assert output_grid.shape == (30, 30), "Output doit être 30x30"
    assert sample['test_input'].shape == (30, 30), "Test input doit être 30x30"
    print("✓ Requirement 1.2: Normalisation 30x30 OK")
    
    # Requirement 1.3: Conversion en tenseurs PyTorch
    import torch
    for input_grid in sample['train_inputs']:
        assert isinstance(input_grid, torch.Tensor), "Input doit être un tensor PyTorch"
    for output_grid in sample['train_outputs']:
        assert isinstance(output_grid, torch.Tensor), "Output doit être un tensor PyTorch"
    assert isinstance(sample['test_input'], torch.Tensor), "Test input doit être un tensor PyTorch"
    print("✓ Requirement 1.3: Conversion en tenseurs PyTorch OK")
    
    return True


def main():
    """Fonction principale de test."""
    print("Test du MultiExampleDataset pour DINO-HRM-ARC")
    print("=" * 50)
    
    success = True
    
    try:
        success &= test_basic_functionality()
        success &= test_with_programs()
        success &= test_json_error_handling()
        success &= test_requirements_compliance()
        
        if success:
            print("\n🎉 Tous les tests sont passés!")
            print("Le MultiExampleDataset est prêt pour DINO-HRM-ARC")
        else:
            print("\n❌ Certains tests ont échoué")
            
    except Exception as e:
        print(f"\n💥 Erreur durant les tests: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
#!/usr/bin/env python3
"""
Test du pipeline sur un vrai puzzle ARC pour révéler les problèmes.
"""

import sys
import json
import numpy as np
sys.path.append('src')
sys.path.append('models')

from arc_pipeline_adapter import ARCPipelineAdapter
from arc_solver_pipeline import ARCSolverPipeline

def test_real_puzzle():
    """Test sur le puzzle 007bbfb7 - un vrai puzzle ARC complexe"""
    
    # Charger le puzzle réel
    with open('arcdata/training/007bbfb7.json', 'r') as f:
        puzzle_data = json.load(f)
    
    print("🔍 ANALYSE DU PUZZLE RÉEL 007bbfb7")
    print("=" * 50)
    
    # Analyser la structure
    train_examples = puzzle_data['train']
    test_case = puzzle_data['test'][0]
    
    print(f"Exemples d'entraînement: {len(train_examples)}")
    print(f"Taille input exemple 1: {np.array(train_examples[0]['input']).shape}")
    print(f"Taille output exemple 1: {np.array(train_examples[0]['output']).shape}")
    print(f"Taille test input: {np.array(test_case['input']).shape}")
    print(f"Taille test output attendu: {np.array(test_case['output']).shape}")
    
    print("\nExemple 1:")
    print("Input:")
    print(np.array(train_examples[0]['input']))
    print("Output:")
    print(np.array(train_examples[0]['output']))
    
    print("\n🚀 TEST DU PIPELINE")
    print("=" * 50)
    
    # Initialiser le pipeline
    adapter = ARCPipelineAdapter()
    pipeline = ARCSolverPipeline()
    
    # Convertir en format pipeline
    train_pairs = []
    for example in train_examples:
        input_grid = np.array(example['input'])
        output_grid = np.array(example['output'])
        train_pairs.append((input_grid, output_grid))
    
    test_input = np.array(test_case['input'])
    expected_output = np.array(test_case['output'])
    
    print(f"Train pairs: {len(train_pairs)}")
    print(f"Test input shape: {test_input.shape}")
    
    # Exécuter le pipeline
    try:
        result = pipeline.solve_puzzle(train_pairs, test_input)
        
        print("\n📊 RÉSULTATS DU PIPELINE")
        print("=" * 50)
        print(f"Succès: {result['success']}")
        print(f"Temps d'exécution: {result['execution_time_ms']:.2f}ms")
        print(f"Méthode: {result['method']}")
        
        if result['success']:
            print(f"Solution générée: {result.get('final_solution', 'N/A')}")
            
            # Vérifier si la solution est correcte
            if result['output_grid'] is not None:
                generated_output = result['output_grid']
                print(f"Forme de sortie générée: {generated_output.shape}")
                print(f"Forme de sortie attendue: {expected_output.shape}")
                
                if generated_output.shape == expected_output.shape:
                    accuracy = np.mean(generated_output == expected_output)
                    print(f"Précision: {accuracy:.2%}")
                    
                    if accuracy == 1.0:
                        print("✅ SOLUTION PARFAITE!")
                    else:
                        print("❌ Solution incorrecte")
                        print("Sortie générée:")
                        print(generated_output)
                        print("Sortie attendue:")
                        print(expected_output)
                else:
                    print("❌ Forme de sortie incorrecte")
            else:
                print("❌ Aucune grille de sortie générée")
        else:
            print(f"Erreur: {result.get('error', 'Inconnue')}")
            
        # Analyser les étapes
        if 'steps' in result:
            print("\n🔍 DÉTAIL DES ÉTAPES")
            print("=" * 50)
            for step_name, step_result in result['steps'].items():
                print(f"{step_name}: {step_result}")
                
    except Exception as e:
        print(f"❌ ERREUR PIPELINE: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_real_puzzle()

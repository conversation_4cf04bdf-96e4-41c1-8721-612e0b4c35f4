"""
Test simple des structures de données sans pytest.
Validation basique des modèles de données du pipeline ARC-Solver.
"""

import numpy as np
import sys
import os

# Ajouter le répertoire src au path
sys.path.append('src')

from data_structures import (
    PuzzleData, AnalysisResult, TemplateResult, SolutionResult, PipelineResult,
    ERROR_MESSAGES, PipelineConfig, ExecutionTimer,
    create_error_result, validate_puzzle_data
)


def test_puzzle_data():
    """Test de la structure PuzzleData."""
    print("Test PuzzleData...")
    
    # Test valide
    train_inputs = [np.array([[1, 2], [3, 4]], dtype=np.int32)]
    train_outputs = [np.array([[2, 1], [4, 3]], dtype=np.int32)]
    test_input = np.array([[5, 6], [7, 8]], dtype=np.int32)
    
    puzzle = PuzzleData(
        puzzle_id="test_001",
        train_inputs=train_inputs,
        train_outputs=train_outputs,
        test_input=test_input
    )
    
    assert puzzle.puzzle_id == "test_001"
    assert len(puzzle.train_inputs) == 1
    assert len(puzzle.train_outputs) == 1
    print("✓ PuzzleData valide créé avec succès")
    
    # Test d'erreur avec données incompatibles
    try:
        PuzzleData(
            puzzle_id="test_error",
            train_inputs=[np.array([[1, 2]]), np.array([[3, 4]])],
            train_outputs=[np.array([[2, 1]])],  # Un seul output pour deux inputs
            test_input=np.array([[5, 6]])
        )
        assert False, "Devrait lever une erreur"
    except ValueError as e:
        print(f"✓ Erreur correctement détectée: {e}")


def test_analysis_result():
    """Test de la structure AnalysisResult."""
    print("\nTest AnalysisResult...")
    
    # Test valide
    result = AnalysisResult(
        category="geometric_transform",
        confidence=0.85,
        detected_transformations=["rotation_90"],
        analysis_details={"rotation_angle": 90},
        execution_time_ms=150.5
    )
    
    assert result.category == "geometric_transform"
    assert result.confidence == 0.85
    print("✓ AnalysisResult valide créé avec succès")
    
    # Test d'erreur avec catégorie invalide
    try:
        AnalysisResult(
            category="invalid_category",
            confidence=0.5,
            detected_transformations=[],
            analysis_details={},
            execution_time_ms=100.0
        )
        assert False, "Devrait lever une erreur"
    except ValueError as e:
        print(f"✓ Erreur correctement détectée: {e}")


def test_template_result():
    """Test de la structure TemplateResult."""
    print("\nTest TemplateResult...")
    
    # Test valide
    result = TemplateResult(
        template="FILL {color_var} [0,0 {width},{height}]",
        variables={"color_var": [1, 2, 3], "width": [10], "height": [10]},
        constraints={"color_var": "must_be_present_in_input"},
        generation_method="rule_based",
        category_used="color_fill"
    )
    
    assert result.template.startswith("FILL")
    assert result.generation_method == "rule_based"
    print("✓ TemplateResult valide créé avec succès")
    
    # Test d'erreur avec méthode invalide
    try:
        TemplateResult(
            template="FILL {color} [0,0 10,10]",
            variables={},
            constraints={},
            generation_method="ai_learned",  # Invalide
            category_used="color_fill"
        )
        assert False, "Devrait lever une erreur"
    except ValueError as e:
        print(f"✓ Erreur correctement détectée: {e}")


def test_solution_result():
    """Test de la structure SolutionResult."""
    print("\nTest SolutionResult...")
    
    # Test valide
    result = SolutionResult(
        success=True,
        solution_command="FILL 1 [0,0 10,10]",
        execution_time_ms=2500.0,
        validation_score=0.95,
        error_message=None,
        method_used="algorithmic_search",
        parameters_tested=150,
        timeout_reached=False
    )
    
    assert result.success is True
    assert result.solution_command == "FILL 1 [0,0 10,10]"
    assert result.method_used == "algorithmic_search"
    print("✓ SolutionResult valide créé avec succès")


def test_execution_timer():
    """Test de l'utilitaire ExecutionTimer."""
    print("\nTest ExecutionTimer...")
    
    timer = ExecutionTimer()
    timer.start()
    
    # Simuler une petite pause
    import time
    time.sleep(0.01)  # 10ms
    
    elapsed = timer.stop()
    assert elapsed >= 10.0  # Au moins 10ms
    assert elapsed < 100.0  # Mais pas trop
    print(f"✓ Timer fonctionne correctement: {elapsed:.2f}ms")


def test_error_handling():
    """Test de la gestion d'erreurs."""
    print("\nTest gestion d'erreurs...")
    
    result = create_error_result(
        error_type="no_pattern_detected",
        puzzle_id="test_error",
        execution_time_ms=500.0
    )
    
    assert result.success is False
    assert result.puzzle_id == "test_error"
    assert result.error_step == "no_pattern_detected"
    print("✓ Création de résultat d'erreur fonctionne")
    
    # Vérifier que tous les messages d'erreur existent
    required_errors = [
        "no_pattern_detected", "template_not_found", "parameter_search_failed",
        "timeout_exceeded", "execution_failed"
    ]
    
    for error_type in required_errors:
        assert error_type in ERROR_MESSAGES
        assert len(ERROR_MESSAGES[error_type]) > 0
    
    print("✓ Tous les messages d'erreur sont définis")


def test_validation():
    """Test de la validation des données."""
    print("\nTest validation...")
    
    # Test valide
    puzzle = PuzzleData(
        puzzle_id="valid_test",
        train_inputs=[np.array([[1, 2], [3, 4]], dtype=np.int32)],
        train_outputs=[np.array([[2, 1], [4, 3]], dtype=np.int32)],
        test_input=np.array([[5, 6], [7, 8]], dtype=np.int32)
    )
    
    assert validate_puzzle_data(puzzle) is True
    print("✓ Validation de puzzle valide réussie")
    
    # Test avec valeurs invalides
    try:
        puzzle_invalid = PuzzleData(
            puzzle_id="invalid_test",
            train_inputs=[np.array([[1, 15], [3, 4]], dtype=np.int32)],  # 15 > 9
            train_outputs=[np.array([[2, 1], [4, 3]], dtype=np.int32)],
            test_input=np.array([[5, 6], [7, 8]], dtype=np.int32)
        )
        validate_puzzle_data(puzzle_invalid)
        assert False, "Devrait lever une erreur"
    except ValueError as e:
        print(f"✓ Validation détecte les valeurs invalides: {e}")


def test_pipeline_result():
    """Test de la structure PipelineResult."""
    print("\nTest PipelineResult...")
    
    analysis = AnalysisResult(
        category="geometric_transform",
        confidence=0.8,
        detected_transformations=["rotation"],
        analysis_details={},
        execution_time_ms=100.0
    )
    
    template = TemplateResult(
        template="ROTATE {angle} {center}",
        variables={"angle": [90], "center": ["5,5"]},
        constraints={},
        generation_method="rule_based",
        category_used="geometric_transform"
    )
    
    solution = SolutionResult(
        success=True,
        solution_command="ROTATE 90 5,5",
        execution_time_ms=1000.0,
        validation_score=1.0,
        error_message=None,
        method_used="algorithmic_search",
        parameters_tested=50,
        timeout_reached=False
    )
    
    pipeline_result = PipelineResult(
        puzzle_id="test_pipeline",
        success=True,
        final_solution="ROTATE 90 5,5",
        analysis_result=analysis,
        template_result=template,
        solution_result=solution,
        total_execution_time_ms=1200.0,
        error_step=None,
        error_message=None
    )
    
    summary = pipeline_result.get_execution_summary()
    
    assert summary["puzzle_id"] == "test_pipeline"
    assert summary["success"] is True
    assert summary["analysis_method"] == "geometric_calculations"
    assert "algorithmes programmés" in summary["transparency_note"]
    print("✓ PipelineResult et résumé fonctionnent correctement")


def test_config():
    """Test de la configuration."""
    print("\nTest PipelineConfig...")
    
    assert PipelineConfig.PATTERN_ANALYSIS_TIMEOUT == 2
    assert PipelineConfig.PARAMETER_RESOLUTION_TIMEOUT == 10
    assert PipelineConfig.TOTAL_PIPELINE_TIMEOUT == 15
    assert PipelineConfig.TARGET_RESOLUTION_RATE == 0.30
    print("✓ Configuration correctement définie")


def main():
    """Exécuter tous les tests."""
    print("=== Tests des Structures de Données ARC-Solver ===")
    print("Validation des modèles de données algorithmiques (pas d'IA)")
    
    try:
        test_puzzle_data()
        test_analysis_result()
        test_template_result()
        test_solution_result()
        test_execution_timer()
        test_error_handling()
        test_validation()
        test_pipeline_result()
        test_config()
        
        print("\n=== TOUS LES TESTS RÉUSSIS ===")
        print("✓ Structures de données validées")
        print("✓ Gestion d'erreurs fonctionnelle")
        print("✓ Validation des données opérationnelle")
        print("✓ Transparence technique respectée")
        
    except Exception as e:
        print(f"\n❌ ÉCHEC DU TEST: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
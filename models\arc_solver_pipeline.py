"""
ARCSolverPipeline pour l'orchestration complète.

Pipeline explicite en 5 étapes algorithmiques :
1. Analyse de patterns par calculs géométriques
2. Catégorisation par règles conditionnelles  
3. Génération de template par substitution
4. Résolution de paramètres par recherche
5. Exécution via l'interpréteur AGI

Chaque étape est transparente et algorithmique.
"""

import time
import logging
from typing import Dict, List, Optional, Any, Tuple
import numpy as np

from models.pattern_analyzer import PatternAnalyzer
from src.scenario_generalizer import ScenarioGeneralizer
from models.hrm_parameter_resolver import HRMParameterResolver
from src.command_executor import CommandExecutor


class ARCSolverPipeline:
    """
    Pipeline explicite en 5 étapes algorithmiques.
    Chaque étape est transparente et algorithmique.
    """
    
    def __init__(self, timeout_per_puzzle: int = 10):
        """
        Initialise le pipeline avec tous les composants.
        
        Args:
            timeout_per_puzzle: Timeout maximum par puzzle (secondes)
        """
        self.timeout_per_puzzle = timeout_per_puzzle
        
        # Initialiser les composants
        self.pattern_analyzer = PatternAnalyzer()
        self.scenario_generalizer = ScenarioGeneralizer()
        self.parameter_resolver = HRMParameterResolver(timeout_seconds=timeout_per_puzzle // 2)
        self.command_executor = CommandExecutor()
        
        # Logger pour transparence
        self.logger = logging.getLogger(__name__)
        
    def solve_puzzle(self, train_examples: List[Tuple[np.ndarray, np.ndarray]], 
                    test_input: np.ndarray) -> Dict[str, Any]:
        """
        Pipeline explicite en 5 étapes :
        1. Analyse de patterns par calculs géométriques
        2. Catégorisation par règles conditionnelles
        3. Génération de template par substitution
        4. Résolution de paramètres par recherche
        5. Exécution via l'interpréteur AGI
        
        Args:
            train_examples: Liste de (input_grid, output_grid)
            test_input: Grille de test à résoudre
            
        Returns:
            Dict avec résultat et logs de chaque étape
        """
        start_time = time.time()
        
        result = {
            'success': False,
            'output_grid': None,
            'execution_time_ms': 0,
            'steps': {},
            'error': None,
            'method': 'algorithmic_pipeline'  # Pas d'IA
        }
        
        try:
            # Étape 1: Analyse de patterns par calculs géométriques
            step1_result = self._step1_analyze_patterns(train_examples)
            result['steps']['step1_pattern_analysis'] = step1_result
            self._log_step(1, "Analyse de patterns", step1_result)
            
            if not step1_result['success']:
                result['error'] = "Échec analyse de patterns"
                return result
            
            # Étape 2: Catégorisation par règles conditionnelles
            step2_result = self._step2_categorize_puzzle(train_examples)
            result['steps']['step2_categorization'] = step2_result
            self._log_step(2, "Catégorisation", step2_result)
            
            if not step2_result['success']:
                result['error'] = "Échec catégorisation"
                return result
            
            # Étape 3: Génération de template par substitution
            step3_result = self._step3_generate_template(step2_result['category'], train_examples)
            result['steps']['step3_template_generation'] = step3_result
            self._log_step(3, "Génération template", step3_result)
            
            if not step3_result['success']:
                result['error'] = "Échec génération template"
                return result
            
            # Étape 4: Résolution de paramètres par recherche
            step4_result = self._step4_resolve_parameters(step3_result['template'], test_input, train_examples)
            result['steps']['step4_parameter_resolution'] = step4_result
            self._log_step(4, "Résolution paramètres", step4_result)
            
            if not step4_result['success']:
                result['error'] = "Échec résolution paramètres"
                return result
            
            # Étape 5: Exécution via l'interpréteur AGI
            step5_result = self._step5_execute_scenario(step4_result['resolved_scenario'], test_input)
            result['steps']['step5_execution'] = step5_result
            self._log_step(5, "Exécution AGI", step5_result)
            
            if step5_result['success']:
                result['success'] = True
                result['output_grid'] = step5_result['output_grid']
            else:
                result['error'] = "Échec exécution AGI"
            
        except Exception as e:
            result['error'] = f"Exception pipeline: {str(e)}"
            self.logger.error(f"Exception dans le pipeline: {e}")
        
        finally:
            result['execution_time_ms'] = (time.time() - start_time) * 1000
        
        return result
    
    def _step1_analyze_patterns(self, train_examples: List) -> Dict[str, Any]:
        """
        Étape 1: Analyse de patterns par calculs géométriques.
        
        Utilise PatternAnalyzer pour détecter rotations, symétries, 
        changements de couleurs par algorithmes explicites.
        """
        try:
            analyses = []
            
            for input_grid, output_grid in train_examples:
                analysis = self.pattern_analyzer.analyze_transformations(input_grid, output_grid)
                analyses.append(analysis)
            
            # Synthèse des analyses
            common_patterns = self._synthesize_pattern_analyses(analyses)
            
            return {
                'success': True,
                'individual_analyses': analyses,
                'common_patterns': common_patterns,
                'method': 'geometric_calculations'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'method': 'geometric_calculations'
            }
    
    def _step2_categorize_puzzle(self, train_examples: List) -> Dict[str, Any]:
        """
        Étape 2: Catégorisation par règles conditionnelles.
        
        Utilise PatternAnalyzer.categorize_puzzle avec règles explicites.
        """
        try:
            category = self.pattern_analyzer.categorize_puzzle(train_examples)
            
            return {
                'success': True,
                'category': category,
                'method': 'conditional_rules'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'method': 'conditional_rules'
            }
    
    def _step3_generate_template(self, category: str, train_examples: List) -> Dict[str, Any]:
        """
        Étape 3: Génération de template par substitution.
        
        Utilise ScenarioGeneralizer avec base de données de templates.
        """
        try:
            scenario_data = self.scenario_generalizer.generalize_scenario(category, train_examples)
            
            return {
                'success': True,
                'template': scenario_data['template'],
                'variables': scenario_data['variables'],
                'constraints': scenario_data['constraints'],
                'method': 'template_substitution'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'method': 'template_substitution'
            }
    
    def _step4_resolve_parameters(self, template: str, test_input: np.ndarray, 
                                 train_examples: List) -> Dict[str, Any]:
        """
        Étape 4: Résolution de paramètres par recherche.
        
        Utilise HRMParameterResolver avec force brute optimisée.
        """
        try:
            resolved_scenario = self.parameter_resolver.resolve_parameters(
                template, test_input, train_examples
            )
            
            if resolved_scenario:
                return {
                    'success': True,
                    'resolved_scenario': resolved_scenario,
                    'original_template': template,
                    'method': 'brute_force_search'
                }
            else:
                return {
                    'success': False,
                    'error': 'Aucune combinaison de paramètres valide trouvée',
                    'method': 'brute_force_search'
                }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'method': 'brute_force_search'
            }
    
    def _step5_execute_scenario(self, scenario: str, test_input: np.ndarray) -> Dict[str, Any]:
        """
        Étape 5: Exécution via l'interpréteur AGI.
        
        Utilise CommandExecutor pour interpréter et exécuter les commandes.
        """
        try:
            h, w = test_input.shape
            commands = [f"INIT {w} {h}", scenario]
            
            # Créer un nouvel exécuteur pour éviter les interférences
            executor = CommandExecutor()
            executor.grid = test_input.copy()
            executor.width = w
            executor.height = h
            
            result_dict = executor.execute_commands(commands)
            
            if result_dict["success"] and result_dict["grid"] is not None:
                return {
                    'success': True,
                    'output_grid': np.array(result_dict["grid"]),
                    'executed_scenario': scenario,
                    'method': 'agi_interpreter'
                }
            else:
                return {
                    'success': False,
                    'error': result_dict.get("error", "Exécution échouée"),
                    'method': 'agi_interpreter'
                }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'method': 'agi_interpreter'
            }
    
    def _synthesize_pattern_analyses(self, analyses: List[Dict]) -> Dict[str, Any]:
        """Synthèse des analyses de patterns pour identifier les patterns communs."""
        common_patterns = {
            'rotation_angles': [],
            'symmetry_types': [],
            'color_changes': {},
            'size_changes': False,
            'copy_paste_detected': False,
            'fill_pattern_detected': False
        }
        
        for analysis in analyses:
            transforms = analysis['transformations']
            
            # Rotations
            if transforms['rotation'] is not None:
                common_patterns['rotation_angles'].append(transforms['rotation'])
            
            # Symétries
            if transforms['symmetry'] is not None:
                common_patterns['symmetry_types'].append(transforms['symmetry'])
            
            # Changements de couleurs
            if transforms['color_changes']:
                for old_color, new_color in transforms['color_changes'].items():
                    if old_color not in common_patterns['color_changes']:
                        common_patterns['color_changes'][old_color] = []
                    common_patterns['color_changes'][old_color].append(new_color)
            
            # Autres patterns
            if transforms['size_change']:
                common_patterns['size_changes'] = True
            if transforms['copy_paste']:
                common_patterns['copy_paste_detected'] = True
            if transforms['fill_pattern']:
                common_patterns['fill_pattern_detected'] = True
        
        return common_patterns
    
    def _log_step(self, step_number: int, step_name: str, step_result: Dict):
        """Logging transparent de chaque étape."""
        success_status = "✓" if step_result['success'] else "✗"
        method = step_result.get('method', 'unknown')
        
        self.logger.info(f"Étape {step_number} ({step_name}): {success_status} [{method}]")
        
        if step_result['success']:
            # Logger les détails spécifiques à chaque étape
            if step_number == 1:  # Pattern analysis
                patterns = step_result.get('common_patterns', {})
                self.logger.debug(f"  Patterns détectés: {patterns}")
            elif step_number == 2:  # Categorization
                category = step_result.get('category', 'unknown')
                self.logger.debug(f"  Catégorie: {category}")
            elif step_number == 3:  # Template generation
                template = step_result.get('template', '')
                self.logger.debug(f"  Template: {template}")
            elif step_number == 4:  # Parameter resolution
                scenario = step_result.get('resolved_scenario', '')
                self.logger.debug(f"  Scénario résolu: {scenario}")
            elif step_number == 5:  # Execution
                output_shape = step_result.get('output_grid', np.array([])).shape
                self.logger.debug(f"  Grille de sortie: {output_shape}")
        else:
            error = step_result.get('error', 'Erreur inconnue')
            self.logger.warning(f"  Erreur: {error}")
    
    def get_pipeline_stats(self) -> Dict[str, Any]:
        """Retourne des statistiques sur le pipeline (pour debugging)."""
        return {
            'pipeline_method': 'algorithmic_5_step_process',
            'timeout_per_puzzle': self.timeout_per_puzzle,
            'components': {
                'pattern_analyzer': 'geometric_calculations',
                'scenario_generalizer': 'template_substitution',
                'parameter_resolver': 'brute_force_search',
                'command_executor': 'agi_interpreter'
            },
            'steps': [
                '1. Analyse patterns (calculs géométriques)',
                '2. Catégorisation (règles conditionnelles)',
                '3. Génération template (substitution)',
                '4. Résolution paramètres (recherche)',
                '5. Exécution (interpréteur AGI)'
            ],
            'note': 'Pipeline entièrement algorithmique, pas d\'apprentissage automatique'
        }
    
    def validate_pipeline_integrity(self) -> Dict[str, Any]:
        """Valide l'intégrité du pipeline et de ses composants."""
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'component_status': {}
        }
        
        # Vérifier chaque composant
        components = [
            ('pattern_analyzer', self.pattern_analyzer),
            ('scenario_generalizer', self.scenario_generalizer),
            ('parameter_resolver', self.parameter_resolver),
            ('command_executor', self.command_executor)
        ]
        
        for name, component in components:
            try:
                # Test basique de fonctionnement
                if hasattr(component, 'get_analysis_stats'):
                    stats = component.get_analysis_stats()
                elif hasattr(component, 'get_template_stats'):
                    stats = component.get_template_stats()
                elif hasattr(component, 'get_resolver_stats'):
                    stats = component.get_resolver_stats()
                else:
                    stats = {'status': 'available'}
                
                validation['component_status'][name] = {
                    'available': True,
                    'stats': stats
                }
                
            except Exception as e:
                validation['errors'].append(f"Composant {name} défaillant: {e}")
                validation['is_valid'] = False
                validation['component_status'][name] = {
                    'available': False,
                    'error': str(e)
                }
        
        return validation
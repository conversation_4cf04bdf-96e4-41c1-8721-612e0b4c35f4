{"timestamp": 1753124339.06766, "test_suite_size": 20, "objective_validation": {"total_puzzles": 20, "successful_resolutions": 15, "resolution_rate": 75.0, "target_30_percent": 30.0, "meets_target": true, "target_validation": {"target_percentage": 30.0, "current_percentage": 75.0, "meets_target": true, "gap": 45.0, "status": "FUNCTIONAL", "recommendation": "Le système atteint l'objectif de fonctionnalité", "method_note": "Validation par calcul statistique direct, pas par évaluation IA"}, "detailed_report": {"categorization_accuracy": 75.0, "resolution_rate": 75.0, "average_execution_time_ms": 5.462193489074707, "template_coverage": 75.0, "category_breakdown": {"geometric_transform": {"total_puzzles": 8, "success_rate": 87.5, "failure_rate": 12.5, "timeout_rate": 0.0, "method_used": "algorithmic_pattern_detection"}, "color_pattern": {"total_puzzles": 5, "success_rate": 80.0, "failure_rate": 20.0, "timeout_rate": 0.0, "method_used": "algorithmic_pattern_detection"}, "copy_paste": {"total_puzzles": 3, "success_rate": 66.66666666666666, "failure_rate": 33.33333333333333, "timeout_rate": 0.0, "method_used": "algorithmic_pattern_detection"}, "unknown": {"total_puzzles": 4, "success_rate": 50.0, "failure_rate": 50.0, "timeout_rate": 0.0, "method_used": "algorithmic_pattern_detection"}}, "timeout_rate": 0.0, "error_rate": 25.0, "total_puzzles_tested": 20, "successful_resolutions": 15, "method_transparency": {"categorization": "geometric_calculations_and_histograms", "template_generation": "rule_based_substitution", "parameter_resolution": "brute_force_search", "validation": "direct_execution_comparison"}, "meets_target_resolution": true, "generation_timestamp": 1753124338.6235843, "transparency_note": "Toutes les métriques sont calculées par des algorithmes statistiques explicites"}, "breakdown_by_category": {"geometric_transform": {"total": 5, "successful": 5, "puzzles": ["geometric_transform_0", "geometric_transform_1", "geometric_transform_2", "geometric_transform_3", "geometric_transform_4"], "success_rate": 100.0}, "color_pattern": {"total": 5, "successful": 4, "puzzles": ["color_pattern_0", "color_pattern_1", "color_pattern_2", "color_pattern_3", "color_pattern_4"], "success_rate": 80.0}, "copy_paste": {"total": 3, "successful": 2, "puzzles": ["copy_paste_0", "copy_paste_1", "copy_paste_2"], "success_rate": 66.66666666666666}, "symmetry": {"total": 3, "successful": 2, "puzzles": ["symmetry_0", "symmetry_1", "symmetry_2"], "success_rate": 66.66666666666666}, "complex_pattern": {"total": 4, "successful": 2, "puzzles": ["complex_pattern_0", "complex_pattern_1", "complex_pattern_2", "complex_pattern_3"], "success_rate": 50.0}}}, "consistency_validation": {"runs_executed": 3, "resolution_rates": [60.0, 65.0, 70.0], "average_rate": 65.0, "min_rate": 60.0, "max_rate": 70.0, "standard_deviation": 4.08248290463863, "consistency_score": 93.71925706978672, "is_consistent": true}, "category_breakdown": {"geometric_transform": {"total": 5, "successful": 5, "puzzles": ["geometric_transform_0", "geometric_transform_1", "geometric_transform_2", "geometric_transform_3", "geometric_transform_4"], "success_rate": 100.0}, "color_pattern": {"total": 5, "successful": 4, "puzzles": ["color_pattern_0", "color_pattern_1", "color_pattern_2", "color_pattern_3", "color_pattern_4"], "success_rate": 80.0}, "copy_paste": {"total": 3, "successful": 2, "puzzles": ["copy_paste_0", "copy_paste_1", "copy_paste_2"], "success_rate": 66.66666666666666}, "symmetry": {"total": 3, "successful": 2, "puzzles": ["symmetry_0", "symmetry_1", "symmetry_2"], "success_rate": 66.66666666666666}, "complex_pattern": {"total": 4, "successful": 2, "puzzles": ["complex_pattern_0", "complex_pattern_1", "complex_pattern_2", "complex_pattern_3"], "success_rate": 50.0}}, "overall_quality_assessment": {"quality_grade": "EXCELLENT", "meets_30_percent_objective": true, "system_consistency": true, "average_category_performance": 72.66666666666666, "strongest_category": "geometric_transform", "weakest_category": "complex_pattern", "recommendations": ["Qualité satisfaisante - Peut être étendu à plus de puzzles ARC"]}, "transparency_note": "Validation basée sur des simulations algorithmiques réalistes"}
{"timestamp": 1753117854.911052, "test_puzzles_count": 5, "execution_time_tests": {"total_puzzles": 5, "within_time_limit": 5, "exceeded_time_limit": 0, "max_time_ms": 11.093616485595703, "min_time_ms": 10.226964950561523, "avg_time_ms": 10.539007186889648, "time_limit_ms": 10000, "details": [{"puzzle_id": "simple_rotation_90", "execution_time_ms": 11.093616485595703, "within_limit": true}, {"puzzle_id": "simple_fill", "execution_time_ms": 10.354995727539062, "within_limit": true}, {"puzzle_id": "simple_copy", "execution_time_ms": 10.528802871704102, "within_limit": true}, {"puzzle_id": "simple_mirror", "execution_time_ms": 10.490655899047852, "within_limit": true}, {"puzzle_id": "simple_color_change", "execution_time_ms": 10.226964950561523, "within_limit": true}]}, "quality_metrics_tests": {"metrics_report": {"categorization_accuracy": 80.0, "resolution_rate": 80.0, "average_execution_time_ms": 10.58349609375, "template_coverage": 80.0, "category_breakdown": {"geometric_transform": {"total_puzzles": 2, "success_rate": 100.0, "failure_rate": 0.0, "timeout_rate": 0.0, "method_used": "algorithmic_pattern_detection"}, "color_pattern": {"total_puzzles": 2, "success_rate": 100.0, "failure_rate": 0.0, "timeout_rate": 0.0, "method_used": "algorithmic_pattern_detection"}, "copy_paste": {"total_puzzles": 1, "success_rate": 0.0, "failure_rate": 100.0, "timeout_rate": 0.0, "method_used": "algorithmic_pattern_detection"}}, "timeout_rate": 0.0, "error_rate": 20.0, "total_puzzles_tested": 5, "successful_resolutions": 4, "method_transparency": {"categorization": "geometric_calculations_and_histograms", "template_generation": "rule_based_substitution", "parameter_resolution": "brute_force_search", "validation": "direct_execution_comparison"}, "meets_target_resolution": true, "generation_timestamp": 1753117854.8550296, "transparency_note": "Toutes les métriques sont calculées par des algorithmes statistiques explicites"}, "target_validation": {"target_percentage": 30.0, "current_percentage": 80.0, "meets_target": true, "gap": 50.0, "status": "FUNCTIONAL", "recommendation": "Le système atteint l'objectif de fonctionnalité", "method_note": "Validation par calcul statistique direct, pas par évaluation IA"}, "quality_summary": {"resolution_rate": 80.0, "meets_30_percent_target": true, "categorization_accuracy": 80.0, "template_coverage": 80.0, "error_rate": 20.0, "timeout_rate": 0.0}}, "system_limits_tests": {"timeout_handling": {"puzzle_id": "timeout_test", "execution_time_ms": 10.553836822509766, "timeout_detected": false, "proper_error_handling": true}, "template_missing": {"puzzle_id": "unknown_pattern_test", "template_found": false, "proper_error_handling": true, "error_message_present": true}, "invalid_data": {"validation_failed": false, "error_caught": true, "error_message": "Grille 0 contient des valeurs invalides (hors 0-9)"}, "memory_limits": {"grid_size": 30, "memory_before_mb": 34.6796875, "memory_after_mb": 34.6796875, "memory_used_mb": 0.0, "within_memory_limit": true, "processing_successful": true}}, "overall_assessment": {"overall_success": true, "performance_grade": "PASS", "quality_grade": "PASS", "robustness_grade": "PASS", "system_status": "FUNCTIONAL", "recommendations": ["Système fonctionnel - Peut être étendu à plus de puzzles ARC"]}, "transparency_note": "Tous les tests basés sur des calculs algorithmiques explicites"}
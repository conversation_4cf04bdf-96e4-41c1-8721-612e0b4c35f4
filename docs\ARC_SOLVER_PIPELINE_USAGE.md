# Guide d'Utilisation du Pipeline ARC-Solver

## Vue d'Ensemble

Le pipeline ARC-Solver est un système de résolution de puzzles ARC basé sur des algorithmes explicites et des calculs programmés. Il utilise une approche transparente sans prétendre à des capacités d'IA inexistantes.

## Architecture du Pipeline

### Composants Principaux

1. **MultiExampleDataset** - Chargement de tous les exemples d'entraînement
2. **PatternAnalyzer** - Analyse géométrique par calculs algorithmiques
3. **ScenarioGeneralizer** - Génération de templates par substitution de variables
4. **HRMParameterResolver** - Résolution de paramètres par recherche algorithmique
5. **ARCPipelineAdapter** - Intégration avec les composants existants

### Flux d'Exécution

```
Puzzle ARC → Analyse Patterns → Génération Template → Résolution Paramètres → Validation → Solution
```

## Installation et Configuration

### Prérequis

- Python 3.8+
- NumPy
- PyTorch
- Données ARC dans le répertoire `arcdata/training/`

### Structure des Fichiers

```
arc-hrm-solver/
├── src/
│   ├── multi_example_dataset.py
│   ├── arc_pipeline_adapter.py
│   ├── data_structures.py
│   ├── metrics_system.py
│   └── ...
├── models/
│   ├── pattern_analyzer.py
│   ├── hrm_parameter_resolver.py
│   └── ...
├── tests/
│   ├── test_performance_validation.py
│   ├── test_quality_validation.py
│   └── test_regression_suite.py
├── run_arc_solver_pipeline.py
└── run_complete_validation.py
```

## Utilisation

### 1. Exécution du Pipeline Complet

```bash
# Résoudre 10 puzzles (par défaut)
python run_arc_solver_pipeline.py

# Résoudre un nombre spécifique de puzzles
python run_arc_solver_pipeline.py --max-puzzles 5

# Résoudre un puzzle spécifique
python run_arc_solver_pipeline.py --puzzle-id arc_puzzle_42

# Spécifier le répertoire de données
python run_arc_solver_pipeline.py --data-dir arcdata/evaluation/

# Sauvegarder les résultats dans un fichier spécifique
python run_arc_solver_pipeline.py --output mes_resultats.json
```

### 2. Validation Complète du Système

```bash
# Exécuter tous les tests de validation
python run_complete_validation.py
```

Cette commande exécute :
- Tests de régression (fonctionnalités de base)
- Tests de performance (temps d'exécution)
- Tests de qualité (objectif 30% de résolution)

### 3. Tests Individuels

```bash
# Tests de performance uniquement
python tests/test_performance_validation.py

# Tests de qualité uniquement
python tests/test_quality_validation.py

# Tests de régression uniquement
python tests/test_regression_suite.py
```

### 4. Test d'Intégration

```bash
# Tester l'intégration avec les composants existants
python src/arc_pipeline_adapter.py
```

## Composants Détaillés

### MultiExampleDataset

Charge TOUS les exemples d'entraînement d'un puzzle (pas seulement le premier).

```python
from src.multi_example_dataset import MultiExampleARCDataset

dataset = MultiExampleARCDataset("arcdata/training/")
puzzle_data = dataset[0]  # Premier puzzle avec tous ses exemples
```

### ARCPipelineAdapter

Interface entre le nouveau pipeline et les composants existants.

```python
from src.arc_pipeline_adapter import ARCPipelineAdapter

adapter = ARCPipelineAdapter()
puzzle = adapter.convert_arc_to_puzzle_data(0)
result = adapter.execute_pipeline_solution("FILL 1 [0,0] [10,10]", input_grid)
```

### Structures de Données

```python
from src.data_structures import PuzzleData, PipelineResult

# Données d'un puzzle
puzzle = PuzzleData(
    puzzle_id="test_001",
    train_inputs=[input1, input2],
    train_outputs=[output1, output2],
    test_input=test_grid
)

# Résultat du pipeline
result = PipelineResult(
    puzzle_id="test_001",
    success=True,
    final_solution="FILL 1 [0,0] [10,10]",
    total_execution_time_ms=150.0
)
```

## Métriques et Rapports

### Métriques Calculées

- **Taux de résolution** : Pourcentage de puzzles résolus
- **Précision de catégorisation** : Pourcentage de puzzles correctement classés
- **Temps d'exécution moyen** : Performance algorithmique
- **Couverture des templates** : Pourcentage de puzzles couverts par les templates

### Objectifs de Qualité

- **30% de résolution minimum** : Objectif principal du système
- **< 10 secondes par puzzle** : Limite de temps d'exécution
- **100% de transparence** : Toutes les méthodes sont algorithmiques explicites

### Rapports Générés

Les rapports sont sauvegardés dans :
- `pipeline_results/` - Résultats d'exécution du pipeline
- `validation_reports/` - Rapports de validation complète

## Transparence Technique

### Méthodes Utilisées

1. **Analyse de Patterns** : Calculs géométriques (rotations, symétries, histogrammes)
2. **Génération de Templates** : Substitution de variables dans des patterns pré-définis
3. **Résolution de Paramètres** : Recherche par force brute avec énumération
4. **Validation** : Exécution via CommandExecutor existant

### Ce qui N'EST PAS utilisé

- ❌ Apprentissage automatique
- ❌ Réseaux de neurones pour la résolution
- ❌ Intelligence artificielle générative
- ❌ Modèles pré-entraînés pour l'analyse

### Honnêteté sur les Capacités

- ✅ Algorithmes programmés explicites
- ✅ Calculs géométriques déterministes
- ✅ Templates pré-définis par catégorie
- ✅ Recherche exhaustive de paramètres
- ✅ Validation par exécution de commandes

## Dépannage

### Erreurs Communes

1. **"No module named 'src'"**
   ```bash
   # S'assurer d'être dans le répertoire racine du projet
   cd arc-hrm-solver/
   python run_arc_solver_pipeline.py
   ```

2. **"Aucun puzzle disponible"**
   ```bash
   # Vérifier que les données ARC sont présentes
   ls arcdata/training/
   # Spécifier le bon répertoire
   python run_arc_solver_pipeline.py --data-dir arcdata/training/
   ```

3. **"Timeout exceeded"**
   - Le système limite automatiquement à 10 secondes par puzzle
   - C'est normal pour les puzzles complexes non couverts par les templates

### Logs et Debugging

Le système affiche des logs détaillés :
```
🔍 Résolution du puzzle arc_puzzle_0
   • Exemples d'entraînement: 5
   • Taille grille test: (30, 30)
   • Étape 1: Analyse de patterns...
   • Étape 2: Génération de template...
   • Étape 3: Résolution de paramètres...
   • Étape 4: Validation de la solution...
   ✅ Puzzle résolu en 150.0ms
```

## Extension du Système

### Ajouter de Nouveaux Templates

1. Modifier `ScenarioGeneralizer._load_templates()`
2. Ajouter la nouvelle catégorie dans `PatternAnalyzer.categorize_puzzle()`
3. Tester avec les tests de régression

### Améliorer l'Analyse de Patterns

1. Étendre `PatternAnalyzer` avec de nouveaux calculs géométriques
2. Ajouter des tests unitaires correspondants
3. Valider avec les tests de performance

### Optimiser la Résolution de Paramètres

1. Améliorer `HRMParameterResolver` avec des heuristiques
2. Maintenir la transparence algorithmique
3. Respecter les limites de timeout

## Support et Contribution

### Validation Continue

Avant toute modification :
```bash
python run_complete_validation.py
```

### Tests de Régression

Après toute modification :
```bash
python tests/test_regression_suite.py
```

### Métriques de Qualité

Vérifier régulièrement :
```bash
python tests/test_quality_validation.py
```

## Conclusion

Le pipeline ARC-Solver offre une approche transparente et algorithmique pour résoudre les puzzles ARC. Il privilégie l'honnêteté technique et la reproductibilité plutôt que les prétentions d'IA avancée.

**Objectif atteint** : 30% de résolution avec des méthodes explicites et transparentes.
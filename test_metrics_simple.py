"""
Test simple du système de métriques sans pytest.
Validation basique du calculateur de métriques honnêtes.
"""

import sys
import tempfile
import json
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.append('src')

from metrics_system import (
    MetricsReport, MetricsCalculator, MetricsReporter,
    validate_target_resolution, compare_metrics_over_time
)
from data_structures import (
    PipelineResult, AnalysisResult, TemplateResult, SolutionResult
)


def create_successful_result(puzzle_id: str, category: str = "geometric_transform") -> PipelineResult:
    """Créer un résultat de pipeline réussi pour les tests."""
    analysis = AnalysisResult(
        category=category,
        confidence=0.8,
        detected_transformations=["rotation"],
        analysis_details={},
        execution_time_ms=100.0
    )
    
    template = TemplateResult(
        template="ROTATE {angle} {center}",
        variables={"angle": [90], "center": ["5,5"]},
        constraints={},
        generation_method="rule_based",
        category_used=category
    )
    
    solution = SolutionResult(
        success=True,
        solution_command="ROTATE 90 5,5",
        execution_time_ms=1000.0,
        validation_score=1.0,
        error_message=None,
        method_used="algorithmic_search",
        parameters_tested=50,
        timeout_reached=False
    )
    
    return PipelineResult(
        puzzle_id=puzzle_id,
        success=True,
        final_solution="ROTATE 90 5,5",
        analysis_result=analysis,
        template_result=template,
        solution_result=solution,
        total_execution_time_ms=1200.0,
        error_step=None,
        error_message=None
    )


def create_failed_result(puzzle_id: str, error_step: str = "parameter_search_failed") -> PipelineResult:
    """Créer un résultat de pipeline échoué pour les tests."""
    analysis = AnalysisResult(
        category="unknown",
        confidence=0.2,
        detected_transformations=[],
        analysis_details={},
        execution_time_ms=100.0
    )
    
    return PipelineResult(
        puzzle_id=puzzle_id,
        success=False,
        final_solution=None,
        analysis_result=analysis,
        template_result=None,
        solution_result=None,
        total_execution_time_ms=500.0,
        error_step=error_step,
        error_message="Échec de la recherche algorithmique"
    )


def test_metrics_calculator():
    """Test du calculateur de métriques."""
    print("Test MetricsCalculator...")
    
    calculator = MetricsCalculator()
    
    # Test avec calculateur vide
    assert calculator.calculate_categorization_accuracy() == 0.0
    assert calculator.calculate_resolution_rate() == 0.0
    assert calculator.calculate_average_execution_time() == 0.0
    print("✓ Calculateur vide fonctionne correctement")
    
    # Ajouter des résultats de test
    for i in range(3):
        result = create_successful_result(f"success_{i}")
        calculator.add_result(result)
    
    for i in range(2):
        result = create_failed_result(f"failed_{i}")
        calculator.add_result(result)
    
    # Vérifier les calculs
    resolution_rate = calculator.calculate_resolution_rate()
    assert resolution_rate == 60.0  # 3 succès sur 5 total
    print(f"✓ Taux de résolution calculé: {resolution_rate}%")
    
    categorization_accuracy = calculator.calculate_categorization_accuracy()
    assert categorization_accuracy == 60.0  # 3 avec template sur 5 total
    print(f"✓ Précision de catégorisation: {categorization_accuracy}%")
    
    avg_time = calculator.calculate_average_execution_time()
    expected_avg = (1200.0 * 3 + 500.0 * 2) / 5  # (3600 + 1000) / 5 = 920
    assert avg_time == expected_avg
    print(f"✓ Temps moyen d'exécution: {avg_time}ms")
    
    template_coverage = calculator.calculate_template_coverage()
    assert template_coverage == 60.0  # 3 avec template sur 5 total
    print(f"✓ Couverture des templates: {template_coverage}%")


def test_metrics_report_generation():
    """Test de génération de rapport."""
    print("\nTest génération de rapport...")
    
    calculator = MetricsCalculator()
    
    # Ajouter des résultats pour atteindre l'objectif 30%
    for i in range(4):  # 4 succès
        result = create_successful_result(f"success_{i}")
        calculator.add_result(result)
    
    for i in range(6):  # 6 échecs
        result = create_failed_result(f"failed_{i}")
        calculator.add_result(result)
    
    # Générer le rapport
    report = calculator.generate_report()
    
    assert isinstance(report, MetricsReport)
    assert report.total_puzzles_tested == 10
    assert report.successful_resolutions == 4
    assert report.resolution_rate == 40.0  # 4/10 = 40%
    assert report.meets_target_resolution is True  # 40% > 30%
    print(f"✓ Rapport généré: {report.resolution_rate}% de résolution")
    print(f"✓ Objectif 30% atteint: {report.meets_target_resolution}")
    
    # Vérifier la transparence
    assert "brute_force_search" in report.method_transparency["parameter_resolution"]
    assert "rule_based_substitution" in report.method_transparency["template_generation"]
    print("✓ Transparence sur les méthodes respectée")


def test_metrics_reporter():
    """Test du générateur de rapports."""
    print("\nTest MetricsReporter...")
    
    # Créer un rapport d'exemple
    report = MetricsReport(
        categorization_accuracy=80.0,
        resolution_rate=35.0,
        average_execution_time_ms=2000.0,
        template_coverage=75.0,
        category_breakdown={
            "geometric_transform": {
                "total_puzzles": 10,
                "success_rate": 60.0,
                "failure_rate": 30.0,
                "timeout_rate": 10.0,
                "method_used": "algorithmic_pattern_detection"
            }
        },
        timeout_rate=8.0,
        error_rate=15.0,
        total_puzzles_tested=50,
        successful_resolutions=17,
        method_transparency={
            "categorization": "geometric_calculations",
            "resolution": "brute_force_search"
        },
        meets_target_resolution=True
    )
    
    # Test de génération de rapport textuel
    temp_dir = tempfile.mkdtemp()
    reporter = MetricsReporter(temp_dir)
    
    text_report = reporter.generate_text_report(report)
    
    assert "RAPPORT DE MÉTRIQUES ARC-SOLVER PIPELINE" in text_report
    assert "TRANSPARENCE TECHNIQUE" in text_report
    assert "algorithmes statistiques explicites" in text_report
    assert "Précision de catégorisation : 80.0%" in text_report
    assert "Taux de résolution : 35.0%" in text_report
    assert "✅ OUI" in text_report  # Objectif atteint
    print("✓ Rapport textuel généré avec transparence")
    
    # Test de sauvegarde JSON
    json_path = reporter.save_report(report, "test_metrics.json")
    assert Path(json_path).exists()
    
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    assert data["categorization_accuracy"] == 80.0
    assert "transparency_note" in data
    print("✓ Sauvegarde JSON réussie")
    
    # Test de sauvegarde texte
    text_path = reporter.save_text_report(report, "test_metrics.txt")
    assert Path(text_path).exists()
    print("✓ Sauvegarde texte réussie")


def test_validation_functions():
    """Test des fonctions de validation."""
    print("\nTest fonctions de validation...")
    
    # Test avec objectif atteint
    report_success = MetricsReport(
        categorization_accuracy=80.0,
        resolution_rate=45.0,  # > 30%
        average_execution_time_ms=1500.0,
        template_coverage=70.0,
        category_breakdown={},
        timeout_rate=5.0,
        error_rate=10.0,
        total_puzzles_tested=100,
        successful_resolutions=45,
        method_transparency={},
        meets_target_resolution=True
    )
    
    validation = validate_target_resolution(report_success)
    assert validation["meets_target"] is True
    assert validation["status"] == "FUNCTIONAL"
    assert validation["gap"] == 15.0  # 45 - 30
    print(f"✓ Validation objectif atteint: {validation['status']}")
    
    # Test avec objectif non atteint
    report_failure = MetricsReport(
        categorization_accuracy=70.0,
        resolution_rate=25.0,  # < 30%
        average_execution_time_ms=2000.0,
        template_coverage=60.0,
        category_breakdown={},
        timeout_rate=10.0,
        error_rate=20.0,
        total_puzzles_tested=100,
        successful_resolutions=25,
        method_transparency={},
        meets_target_resolution=False
    )
    
    validation_fail = validate_target_resolution(report_failure)
    assert validation_fail["meets_target"] is False
    assert validation_fail["status"] == "NEEDS_IMPROVEMENT"
    print(f"✓ Validation objectif non atteint: {validation_fail['status']}")


def test_metrics_comparison():
    """Test de comparaison de métriques."""
    print("\nTest comparaison de métriques...")
    
    report1 = MetricsReport(
        categorization_accuracy=70.0,
        resolution_rate=25.0,
        average_execution_time_ms=2500.0,
        template_coverage=60.0,
        category_breakdown={},
        timeout_rate=15.0,
        error_rate=25.0,
        total_puzzles_tested=50,
        successful_resolutions=12,
        method_transparency={},
        meets_target_resolution=False
    )
    
    report2 = MetricsReport(
        categorization_accuracy=85.0,
        resolution_rate=40.0,
        average_execution_time_ms=2000.0,
        template_coverage=80.0,
        category_breakdown={},
        timeout_rate=10.0,
        error_rate=15.0,
        total_puzzles_tested=100,
        successful_resolutions=40,
        method_transparency={},
        meets_target_resolution=True
    )
    
    comparison = compare_metrics_over_time([report1, report2])
    
    assert comparison["resolution_rate_evolution"]["trend"] == "IMPROVEMENT"
    assert comparison["resolution_rate_evolution"]["change"] == 15.0
    assert comparison["execution_time_evolution"]["trend"] == "FASTER"
    print("✓ Comparaison de métriques dans le temps fonctionne")


def main():
    """Exécuter tous les tests."""
    print("=== Tests du Système de Métriques Honnêtes ===")
    print("Validation des calculs statistiques explicites (pas d'IA)")
    
    try:
        test_metrics_calculator()
        test_metrics_report_generation()
        test_metrics_reporter()
        test_validation_functions()
        test_metrics_comparison()
        
        print("\n=== TOUS LES TESTS RÉUSSIS ===")
        print("✓ Calculateur de métriques validé")
        print("✓ Génération de rapports fonctionnelle")
        print("✓ Validation de l'objectif 30% opérationnelle")
        print("✓ Transparence technique respectée")
        print("✓ Métriques honnêtes sans prétention IA")
        
    except Exception as e:
        print(f"\n❌ ÉCHEC DU TEST: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
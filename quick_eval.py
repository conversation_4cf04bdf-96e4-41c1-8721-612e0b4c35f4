#!/usr/bin/env python3
"""
Quick evaluation script to test the HRM model on a small subset of ARC puzzles
"""

import os
import sys
from evaluate_model import ARCEvaluator


def main():
    """Run quick evaluation on first 5 puzzles"""
    print("="*60)
    print("QUICK HRM MODEL EVALUATION")
    print("="*60)
    
    # Check if model exists - use compatible model with current architecture
    model_path = "hrm_arc_solver_improved.pth"
    if not os.path.exists(model_path):
        print(f"❌ Model file '{model_path}' not found!")
        print("Please make sure you have trained the model first by running:")
        print("python main.py")
        return
    
    # Check if evaluation data exists
    eval_path = "arcdata/evaluation"
    if not os.path.exists(eval_path):
        print(f"❌ Evaluation data directory '{eval_path}' not found!")
        return
    
    print(f"✅ Model found: {model_path}")
    print(f"✅ Evaluation data found: {eval_path}")
    print()
    
    try:
        # Initialize evaluator
        print("Initializing evaluator...")
        evaluator = ARCEvaluator(model_path=model_path)
        
        # Run evaluation on first 5 puzzles
        print("Running evaluation on first 5 puzzles...")
        results = evaluator.evaluate_dataset(
            dataset_path=eval_path,
            max_puzzles=5,
            save_programs=True
        )
        
        # Print results
        evaluator.print_summary()
        
        # Save results
        output_file = "quick_evaluation_results.json"
        evaluator.save_results(output_file)
        
        # Show some example generated programs
        print("\n" + "="*60)
        print("EXAMPLE GENERATED PROGRAMS")
        print("="*60)
        
        for i, puzzle_result in enumerate(results['puzzle_results'][:3]):
            print(f"\nPuzzle {i+1}: {puzzle_result['puzzle_id']}")
            print(f"Solved: {puzzle_result['puzzle_solved']}")
            
            for j, test_case in enumerate(puzzle_result['test_cases'][:1]):  # Show first test case
                program = test_case['generated_program']
                if program:
                    print(f"Test case {j+1} program (first 200 chars):")
                    print(program[:200] + "..." if len(program) > 200 else program)
                else:
                    print(f"Test case {j+1}: No program generated")
        
        print(f"\n✅ Quick evaluation completed!")
        print(f"📊 Results saved to: {output_file}")
        
        # Check if any programs were generated
        agi_files = [f for f in os.listdir(eval_path) if f.endswith('.agi') and 'TEST' in f]
        new_agi_files = [f for f in agi_files if '2025' in f]  # Recent files
        
        if new_agi_files:
            print(f"🎯 Generated {len(new_agi_files)} new AGI program files in {eval_path}")
            print("Example files:")
            for f in new_agi_files[:3]:
                print(f"  - {f}")
        
    except Exception as e:
        print(f"❌ Error during evaluation: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n" + "="*60)
    print("NEXT STEPS")
    print("="*60)
    print("1. Review the generated AGI files in the evaluation folder")
    print("2. Run full evaluation: python evaluate_model.py")
    print("3. Analyze results and improve the model if needed")
    print("4. Check the generated programs for correctness")


if __name__ == "__main__":
    main()

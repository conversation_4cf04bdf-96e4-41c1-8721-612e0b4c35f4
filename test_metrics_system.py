#!/usr/bin/env python3
"""
Tests unitaires pour le système de métriques honnêtes.

Valide le calcul de précision de catégorisation,
le taux de résolution, les temps d'exécution,
et la validation de l'objectif 30% de puzzles résolus.
"""

import sys
import os
sys.path.append('.')

import numpy as np
import tempfile
import json
from models.metrics_system import (
    CategorizationMetrics, ResolutionMetrics, ExecutionTimeMetrics,
    TemplateMetrics, HonestMetricsSystem
)
from models.data_structures import (
    PuzzleData, PipelineResult, AnalysisResult, CategorizationResult,
    TemplateResult, SolutionResult, ExecutionResult
)


def test_categorization_metrics():
    """Test des métriques de précision de catégorisation."""
    print("=== Test CategorizationMetrics ===")
    
    metrics = CategorizationMetrics()
    
    # Ajouter des catégorisations
    metrics.add_categorization("color_pattern", "color_pattern")  # Correct
    metrics.add_categorization("geometric_transform", "color_pattern")  # Incorrect
    metrics.add_categorization("color_pattern", "color_pattern")  # Correct
    metrics.add_categorization("fill_pattern")  # Sans vérité terrain
    
    # Vérifications
    assert metrics.total_puzzles == 4, "Nombre total incorrect"
    assert metrics.correctly_categorized == 2, "Nombre correct incorrect"
    assert metrics.get_precision() == 2/3, "Précision incorrecte (2 corrects sur 3 avec vérité terrain)"
    assert metrics.get_coverage() == 1.0, "Couverture doit être 100%"
    
    # Vérifier la distribution
    expected_distribution = {"color_pattern": 2, "geometric_transform": 1, "fill_pattern": 1}
    assert metrics.category_distribution == expected_distribution, "Distribution incorrecte"
    
    print("✓ Calcul de précision par comparaison algorithmique")
    
    # Test rapport
    report = metrics.to_dict()
    assert report['method'] == 'conditional_rules_classification', "Méthode incorrecte"
    assert 'pas d\'apprentissage' in report['note'], "Note de transparence manquante"
    
    print("✓ Rapport avec transparence méthodologique")
    
    return True


def test_resolution_metrics():
    """Test des métriques de taux de résolution."""
    print("\n=== Test ResolutionMetrics ===")
    
    metrics = ResolutionMetrics()
    
    # Ajouter des tentatives de résolution
    metrics.add_resolution_attempt(success=True, combinations_tested=10)
    metrics.add_resolution_attempt(success=False, combinations_tested=50, timed_out=True)
    metrics.add_resolution_attempt(success=True, combinations_tested=30)
    metrics.add_resolution_attempt(success=False, combinations_tested=100)
    
    # Vérifications
    assert metrics.total_attempts == 4, "Nombre total d'tentatives incorrect"
    assert metrics.successful_resolutions == 2, "Nombre de résolutions réussies incorrect"
    assert metrics.get_resolution_rate() == 0.5, "Taux de résolution incorrect"
    assert metrics.get_timeout_rate() == 0.25, "Taux de timeout incorrect"
    assert metrics.average_combinations_tested == 47.5, "Moyenne combinaisons incorrecte"
    
    print("✓ Calcul de taux par recherche algorithmique")
    
    # Test rapport
    report = metrics.to_dict()
    assert report['method'] == 'brute_force_parameter_search', "Méthode incorrecte"
    assert 'recherche algorithmique exhaustive' in report['note'], "Note de transparence manquante"
    
    print("✓ Rapport avec méthode de recherche explicite")
    
    return True


def test_execution_time_metrics():
    """Test des métriques de temps d'exécution."""
    print("\n=== Test ExecutionTimeMetrics ===")
    
    metrics = ExecutionTimeMetrics()
    
    # Ajouter des temps d'exécution
    metrics.add_component_time("pattern_analyzer", 10.0)
    metrics.add_component_time("pattern_analyzer", 20.0)
    metrics.add_component_time("parameter_resolver", 100.0)
    metrics.add_component_time("parameter_resolver", 200.0)
    
    metrics.add_pipeline_time(150.0)
    metrics.add_pipeline_time(250.0)
    
    # Vérifications
    assert metrics.get_component_average("pattern_analyzer") == 15.0, "Moyenne pattern_analyzer incorrecte"
    assert metrics.get_component_average("parameter_resolver") == 150.0, "Moyenne parameter_resolver incorrecte"
    assert metrics.get_pipeline_average() == 200.0, "Moyenne pipeline incorrecte"
    
    # Test percentiles
    percentiles = metrics.get_component_percentiles("parameter_resolver")
    assert percentiles['p50'] == 100.0, "Médiane incorrecte"  # Premier élément dans liste triée [100, 200]
    
    print("✓ Mesure de temps par chronomètre système")
    
    # Test rapport
    report = metrics.to_dict()
    assert report['method'] == 'algorithmic_time_measurement', "Méthode incorrecte"
    assert 'chronomètre système' in report['note'], "Note de transparence manquante"
    assert 'pattern_analyzer' in report['components'], "Composant pattern_analyzer manquant"
    
    print("✓ Rapport avec mesures transparentes")
    
    return True


def test_template_metrics():
    """Test des métriques de couverture des templates."""
    print("\n=== Test TemplateMetrics ===")
    
    metrics = TemplateMetrics()
    
    # Ajouter des utilisations de templates
    metrics.add_template_usage("color_pattern", True)
    metrics.add_template_usage("color_pattern", True)
    metrics.add_template_usage("geometric_transform", True)
    metrics.add_template_usage("unknown_category", False)
    
    # Vérifications
    assert metrics.total_puzzles == 4, "Nombre total incorrect"
    assert metrics.covered_puzzles == 3, "Nombre couvert incorrect"
    assert metrics.get_coverage_rate() == 0.75, "Taux de couverture incorrect"
    assert metrics.get_most_used_template() == "color_pattern", "Template le plus utilisé incorrect"
    assert "unknown_category" in metrics.uncovered_categories, "Catégorie non couverte manquante"
    
    print("✓ Calcul de couverture par base de données statique")
    
    # Test rapport
    report = metrics.to_dict()
    assert report['method'] == 'template_database_coverage', "Méthode incorrecte"
    assert 'base de données statique' in report['note'], "Note de transparence manquante"
    
    print("✓ Rapport avec méthode de base de données explicite")
    
    return True


def test_honest_metrics_system():
    """Test du système de métriques honnêtes complet."""
    print("\n=== Test HonestMetricsSystem ===")
    
    system = HonestMetricsSystem()
    
    # Créer des données de test
    puzzle_data = PuzzleData(
        puzzle_id="test_001",
        train_examples=[(np.array([[1, 2]]), np.array([[3, 4]]))],
        test_input=np.array([[5, 6]])
    )
    
    # Créer des résultats de pipeline
    analysis = AnalysisResult(success=True, execution_time_ms=10.0)
    categorization = CategorizationResult(success=True, category="color_pattern")
    template = TemplateResult(success=True, template="REPLACE {old_color} {new_color}")
    solution = SolutionResult(success=True, combinations_tested=25)
    execution = ExecutionResult(success=True, output_grid=np.array([[7, 8]]))
    
    result1 = PipelineResult(
        success=True,
        puzzle_id="test_001",
        execution_time_ms=100.0,
        step1_analysis=analysis,
        step2_categorization=categorization,
        step3_template=template,
        step4_solution=solution,
        step5_execution=execution
    )
    
    # Résultat échoué
    failed_solution = SolutionResult(success=False, combinations_tested=100, error="Timeout dépassé")
    result2 = PipelineResult(
        success=False,
        puzzle_id="test_002",
        execution_time_ms=200.0,
        step1_analysis=analysis,
        step2_categorization=categorization,
        step3_template=template,
        step4_solution=failed_solution
    )
    
    # Enregistrer les résultats
    system.record_pipeline_result(puzzle_data, result1)
    system.record_pipeline_result(puzzle_data, result2)
    
    # Vérifications
    assert system.total_puzzles_processed == 2, "Nombre total de puzzles incorrect"
    assert system.successful_puzzles == 1, "Nombre de puzzles réussis incorrect"
    assert system.calculate_overall_success_rate() == 0.5, "Taux de réussite global incorrect"
    
    print("✓ Enregistrement et calcul des métriques globales")
    
    return True


def test_30_percent_objective_validation():
    """Test de la validation de l'objectif 30% de puzzles résolus."""
    print("\n=== Test Validation Objectif 30% ===")
    
    system = HonestMetricsSystem()
    
    # Simuler des résultats pour atteindre 30%
    puzzle_data = PuzzleData(
        puzzle_id="test",
        train_examples=[(np.array([[1]]), np.array([[2]]))],
        test_input=np.array([[3]])
    )
    
    # 3 succès sur 10 = 30%
    for i in range(10):
        success = i < 3  # 3 premiers réussissent
        result = PipelineResult(
            success=success,
            puzzle_id=f"test_{i}",
            execution_time_ms=100.0
        )
        system.record_pipeline_result(puzzle_data, result)
    
    # Validation de l'objectif
    validation = system.validate_30_percent_objective()
    
    assert validation['objective_met'] == True, "Objectif 30% doit être atteint"
    assert validation['current_success_rate'] == 0.3, "Taux actuel incorrect"
    assert validation['target_success_rate'] == 0.3, "Taux cible incorrect"
    assert validation['gap_to_target'] == 0.0, "Écart à la cible incorrect"
    
    print("✓ Validation objectif 30% par comptage simple")
    
    # Test avec objectif non atteint
    system2 = HonestMetricsSystem()
    
    # 2 succès sur 10 = 20%
    for i in range(10):
        success = i < 2  # 2 premiers réussissent
        result = PipelineResult(
            success=success,
            puzzle_id=f"test_{i}",
            execution_time_ms=100.0
        )
        system2.record_pipeline_result(puzzle_data, result)
    
    validation2 = system2.validate_30_percent_objective()
    
    assert validation2['objective_met'] == False, "Objectif 30% ne doit pas être atteint"
    assert validation2['current_success_rate'] == 0.2, "Taux actuel incorrect"
    print(f"DEBUG: gap_to_target = {validation2['gap_to_target']}")
    assert abs(validation2['gap_to_target'] - 0.1) < 0.001, f"Écart à la cible incorrect: {validation2['gap_to_target']}"
    
    print("✓ Validation objectif non atteint")
    
    return True


def test_comprehensive_report():
    """Test du rapport complet avec transparence sur les méthodes."""
    print("\n=== Test Rapport Complet ===")
    
    system = HonestMetricsSystem()
    
    # Ajouter quelques données
    puzzle_data = PuzzleData(
        puzzle_id="test",
        train_examples=[(np.array([[1]]), np.array([[2]]))],
        test_input=np.array([[3]])
    )
    
    analysis = AnalysisResult(success=True, execution_time_ms=15.0)
    categorization = CategorizationResult(success=True, category="color_pattern")
    template = TemplateResult(success=True)
    solution = SolutionResult(success=True, combinations_tested=20)
    
    result = PipelineResult(
        success=True,
        puzzle_id="test",
        execution_time_ms=50.0,
        step1_analysis=analysis,
        step2_categorization=categorization,
        step3_template=template,
        step4_solution=solution
    )
    
    system.record_pipeline_result(puzzle_data, result)
    
    # Générer le rapport
    report = system.generate_comprehensive_report()
    
    # Vérifications de structure
    assert 'timestamp' in report, "Timestamp manquant"
    assert 'system_method' in report, "Méthode système manquante"
    assert 'global_metrics' in report, "Métriques globales manquantes"
    assert 'objective_validation' in report, "Validation objectif manquante"
    assert 'methodology_transparency' in report, "Transparence méthodologique manquante"
    
    # Vérifier la transparence
    transparency = report['methodology_transparency']
    assert 'no_ai_involved' in transparency, "Déclaration 'pas d'IA' manquante"
    assert 'Aucun apprentissage automatique' in transparency['no_ai_involved'], "Message transparence incorrect"
    
    print("✓ Rapport complet avec transparence méthodologique")
    
    # Test sauvegarde
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_path = f.name
    
    try:
        system.save_report(temp_path)
        
        # Vérifier que le fichier a été créé et est valide
        with open(temp_path, 'r') as f:
            saved_report = json.load(f)
        
        assert saved_report['system_method'] == report['system_method'], "Rapport sauvegardé incorrect"
        print("✓ Sauvegarde rapport JSON")
        
    finally:
        os.unlink(temp_path)
    
    return True


def test_performance_bottlenecks():
    """Test de l'identification des goulots d'étranglement."""
    print("\n=== Test Goulots d'Étranglement ===")
    
    system = HonestMetricsSystem()
    
    # Simuler des métriques avec des problèmes
    system.categorization_metrics.total_puzzles = 10
    system.categorization_metrics.correctly_categorized = 5  # 50% < 80%
    
    system.resolution_metrics.total_attempts = 10
    system.resolution_metrics.successful_resolutions = 3  # 30% < 50%
    
    system.template_metrics.total_puzzles = 10
    system.template_metrics.covered_puzzles = 6  # 60% < 70%
    
    system.execution_time_metrics.total_pipeline_times = [9000.0, 10000.0]  # > 8000ms
    
    # Identifier les goulots
    bottlenecks = system.get_performance_bottlenecks()
    
    # Vérifications
    bottleneck_components = [b['component'] for b in bottlenecks]
    assert 'categorization' in bottleneck_components, "Goulot catégorisation manquant"
    assert 'parameter_resolution' in bottleneck_components, "Goulot résolution manquant"
    assert 'template_coverage' in bottleneck_components, "Goulot couverture manquant"
    assert 'execution_time' in bottleneck_components, "Goulot temps manquant"
    
    # Vérifier les recommandations
    for bottleneck in bottlenecks:
        assert 'recommended_action' in bottleneck, f"Action recommandée manquante pour {bottleneck['component']}"
        assert len(bottleneck['recommended_action']) > 10, f"Action trop courte pour {bottleneck['component']}"
    
    print("✓ Identification des goulots d'étranglement")
    
    return True


def test_requirements_compliance():
    """Vérifie la conformité aux requirements."""
    print("\n=== Vérification des Requirements ===")
    
    system = HonestMetricsSystem()
    
    # Requirement 7.1: Calcul de précision de catégorisation
    system.categorization_metrics.add_categorization("color_pattern", "color_pattern")
    precision = system.categorization_metrics.get_precision()
    assert isinstance(precision, float), "Précision doit être un float"
    assert 0.0 <= precision <= 1.0, "Précision doit être entre 0 et 1"
    print("✓ Requirement 7.1: Calcul de précision de catégorisation")
    
    # Requirement 7.2: Calcul du taux de résolution
    system.resolution_metrics.add_resolution_attempt(True, 10)
    resolution_rate = system.resolution_metrics.get_resolution_rate()
    assert isinstance(resolution_rate, float), "Taux de résolution doit être un float"
    assert 0.0 <= resolution_rate <= 1.0, "Taux de résolution doit être entre 0 et 1"
    print("✓ Requirement 7.2: Calcul du taux de résolution")
    
    # Requirement 7.3: Mesure des temps d'exécution algorithmique
    system.execution_time_metrics.add_pipeline_time(100.0)
    avg_time = system.execution_time_metrics.get_pipeline_average()
    assert isinstance(avg_time, float), "Temps moyen doit être un float"
    assert avg_time > 0, "Temps moyen doit être positif"
    print("✓ Requirement 7.3: Mesure des temps d'exécution")
    
    # Requirement 7.4: Calcul de couverture des templates
    system.template_metrics.add_template_usage("color_pattern", True)
    coverage = system.template_metrics.get_coverage_rate()
    assert isinstance(coverage, float), "Couverture doit être un float"
    assert 0.0 <= coverage <= 1.0, "Couverture doit être entre 0 et 1"
    print("✓ Requirement 7.4: Calcul de couverture des templates")
    
    # Requirement 7.5: Rapports de métriques avec transparence
    report = system.generate_comprehensive_report()
    assert 'methodology_transparency' in report, "Transparence méthodologique manquante"
    transparency = report['methodology_transparency']
    for method_name, description in transparency.items():
        if method_name != 'no_ai_involved':
            assert isinstance(description, str), f"Description {method_name} doit être une string"
            assert len(description) > 5, f"Description {method_name} trop courte"
    print("✓ Requirement 7.5: Rapports avec transparence sur les méthodes")
    
    # Requirement 7.6: Validation de l'objectif 30% de puzzles résolus
    validation = system.validate_30_percent_objective()
    assert 'objective_met' in validation, "Validation objectif manquante"
    assert 'current_success_rate' in validation, "Taux actuel manquant"
    assert 'target_success_rate' in validation, "Taux cible manquant"
    assert validation['target_success_rate'] == 0.3, "Taux cible doit être 30%"
    print("✓ Requirement 7.6: Validation de l'objectif 30%")
    
    return True


def main():
    """Fonction principale de test."""
    print("Tests unitaires du système de métriques honnêtes")
    print("=" * 60)
    
    success = True
    
    try:
        success &= test_categorization_metrics()
        success &= test_resolution_metrics()
        success &= test_execution_time_metrics()
        success &= test_template_metrics()
        success &= test_honest_metrics_system()
        success &= test_30_percent_objective_validation()
        success &= test_comprehensive_report()
        success &= test_performance_bottlenecks()
        success &= test_requirements_compliance()
        
        if success:
            print("\n🎉 Tous les tests du système de métriques sont passés!")
            print("Métriques honnêtes avec transparence méthodologique validées")
        else:
            print("\n❌ Certains tests ont échoué")
            
    except Exception as e:
        print(f"\n💥 Erreur durant les tests: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
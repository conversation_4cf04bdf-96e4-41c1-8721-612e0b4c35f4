"""
Système de métriques honnêtes pour le pipeline ARC.

Calcul de métriques transparentes avec méthodes algorithmiques explicites.
Aucune promesse d'IA, seulement des calculs statistiques honnêtes.
"""

import time
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import numpy as np

from models.data_structures import PipelineResult, PuzzleData


@dataclass
class CategorizationMetrics:
    """
    Métriques de précision de catégorisation.
    
    Calcule le pourcentage de puzzles correctement classés
    par les règles conditionnelles algorithmiques.
    """
    total_puzzles: int = 0
    correctly_categorized: int = 0
    puzzles_with_ground_truth: int = 0
    category_distribution: Dict[str, int] = field(default_factory=dict)
    method: str = "conditional_rules_classification"
    
    def add_categorization(self, predicted_category: str, actual_category: Optional[str] = None):
        """Ajoute une catégorisation à évaluer."""
        self.total_puzzles += 1
        
        # Compter la distribution des catégories
        if predicted_category not in self.category_distribution:
            self.category_distribution[predicted_category] = 0
        self.category_distribution[predicted_category] += 1
        
        # Si on a la vraie catégorie, compter la précision
        if actual_category:
            self.puzzles_with_ground_truth += 1
            if predicted_category == actual_category:
                self.correctly_categorized += 1
    
    def get_precision(self) -> float:
        """Retourne la précision de catégorisation (0.0 à 1.0)."""
        if self.puzzles_with_ground_truth == 0:
            return 0.0
        return self.correctly_categorized / self.puzzles_with_ground_truth
    
    def get_coverage(self) -> float:
        """Retourne le taux de couverture (puzzles classés / total)."""
        classified_puzzles = sum(self.category_distribution.values())
        if self.total_puzzles == 0:
            return 0.0
        return classified_puzzles / self.total_puzzles
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertit en dictionnaire pour rapport."""
        return {
            'precision': self.get_precision(),
            'coverage': self.get_coverage(),
            'total_puzzles': self.total_puzzles,
            'correctly_categorized': self.correctly_categorized,
            'category_distribution': self.category_distribution,
            'method': self.method,
            'note': 'Précision calculée par comparaison algorithmique, pas d\'apprentissage'
        }


@dataclass
class ResolutionMetrics:
    """
    Métriques de taux de résolution.
    
    Calcule le pourcentage de paramètres trouvés
    par la recherche algorithmique par force brute.
    """
    total_attempts: int = 0
    successful_resolutions: int = 0
    average_combinations_tested: float = 0.0
    timeout_count: int = 0
    method: str = "brute_force_parameter_search"
    
    def add_resolution_attempt(self, success: bool, combinations_tested: int, 
                             timed_out: bool = False):
        """Ajoute une tentative de résolution à évaluer."""
        self.total_attempts += 1
        
        if success:
            self.successful_resolutions += 1
        
        if timed_out:
            self.timeout_count += 1
        
        # Moyenne mobile des combinaisons testées
        self.average_combinations_tested = (
            (self.average_combinations_tested * (self.total_attempts - 1) + combinations_tested)
            / self.total_attempts
        )
    
    def get_resolution_rate(self) -> float:
        """Retourne le taux de résolution (0.0 à 1.0)."""
        if self.total_attempts == 0:
            return 0.0
        return self.successful_resolutions / self.total_attempts
    
    def get_timeout_rate(self) -> float:
        """Retourne le taux de timeout (0.0 à 1.0)."""
        if self.total_attempts == 0:
            return 0.0
        return self.timeout_count / self.total_attempts
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertit en dictionnaire pour rapport."""
        return {
            'resolution_rate': self.get_resolution_rate(),
            'timeout_rate': self.get_timeout_rate(),
            'total_attempts': self.total_attempts,
            'successful_resolutions': self.successful_resolutions,
            'average_combinations_tested': self.average_combinations_tested,
            'timeout_count': self.timeout_count,
            'method': self.method,
            'note': 'Taux calculé par recherche algorithmique exhaustive, pas d\'apprentissage'
        }


@dataclass
class ExecutionTimeMetrics:
    """
    Métriques de temps d'exécution algorithmique.
    
    Mesure les temps d'exécution de chaque composant
    avec transparence sur les méthodes utilisées.
    """
    component_times: Dict[str, List[float]] = field(default_factory=dict)
    total_pipeline_times: List[float] = field(default_factory=list)
    method: str = "algorithmic_time_measurement"
    
    def add_component_time(self, component_name: str, execution_time_ms: float):
        """Ajoute un temps d'exécution de composant."""
        if component_name not in self.component_times:
            self.component_times[component_name] = []
        self.component_times[component_name].append(execution_time_ms)
    
    def add_pipeline_time(self, total_time_ms: float):
        """Ajoute un temps d'exécution total du pipeline."""
        self.total_pipeline_times.append(total_time_ms)
    
    def get_component_average(self, component_name: str) -> float:
        """Retourne le temps moyen d'un composant."""
        times = self.component_times.get(component_name, [])
        return sum(times) / len(times) if times else 0.0
    
    def get_pipeline_average(self) -> float:
        """Retourne le temps moyen total du pipeline."""
        return sum(self.total_pipeline_times) / len(self.total_pipeline_times) if self.total_pipeline_times else 0.0
    
    def get_component_percentiles(self, component_name: str) -> Dict[str, float]:
        """Retourne les percentiles d'un composant."""
        times = self.component_times.get(component_name, [])
        if not times:
            return {'p50': 0.0, 'p90': 0.0, 'p95': 0.0}
        
        sorted_times = sorted(times)
        n = len(sorted_times)
        
        return {
            'p50': sorted_times[max(0, int(n * 0.5) - 1)] if n > 1 else sorted_times[0],
            'p90': sorted_times[max(0, int(n * 0.9) - 1)] if n > 1 else sorted_times[0],
            'p95': sorted_times[max(0, int(n * 0.95) - 1)] if n > 1 else sorted_times[0]
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertit en dictionnaire pour rapport."""
        component_stats = {}
        for component_name in self.component_times:
            component_stats[component_name] = {
                'average_ms': self.get_component_average(component_name),
                'count': len(self.component_times[component_name]),
                'percentiles': self.get_component_percentiles(component_name)
            }
        
        return {
            'pipeline_average_ms': self.get_pipeline_average(),
            'pipeline_count': len(self.total_pipeline_times),
            'components': component_stats,
            'method': self.method,
            'note': 'Temps mesurés par chronomètre système, pas d\'estimation IA'
        }


@dataclass
class TemplateMetrics:
    """
    Métriques de couverture des templates.
    
    Calcule le pourcentage de puzzles couverts
    par la base de données de templates pré-définis.
    """
    total_puzzles: int = 0
    covered_puzzles: int = 0
    template_usage: Dict[str, int] = field(default_factory=dict)
    uncovered_categories: List[str] = field(default_factory=list)
    method: str = "template_database_coverage"
    
    def add_template_usage(self, category: str, template_found: bool):
        """Ajoute l'utilisation d'un template."""
        self.total_puzzles += 1
        
        if template_found:
            self.covered_puzzles += 1
            if category not in self.template_usage:
                self.template_usage[category] = 0
            self.template_usage[category] += 1
        else:
            if category not in self.uncovered_categories:
                self.uncovered_categories.append(category)
    
    def get_coverage_rate(self) -> float:
        """Retourne le taux de couverture des templates."""
        if self.total_puzzles == 0:
            return 0.0
        return self.covered_puzzles / self.total_puzzles
    
    def get_most_used_template(self) -> Optional[str]:
        """Retourne le template le plus utilisé."""
        if not self.template_usage:
            return None
        return max(self.template_usage, key=self.template_usage.get)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertit en dictionnaire pour rapport."""
        return {
            'coverage_rate': self.get_coverage_rate(),
            'total_puzzles': self.total_puzzles,
            'covered_puzzles': self.covered_puzzles,
            'template_usage': self.template_usage,
            'uncovered_categories': self.uncovered_categories,
            'most_used_template': self.get_most_used_template(),
            'method': self.method,
            'note': 'Couverture calculée par base de données statique, pas d\'apprentissage'
        }


class HonestMetricsSystem:
    """
    Système de métriques honnêtes pour le pipeline ARC.
    
    Collecte et calcule des métriques transparentes
    avec méthodes algorithmiques explicites.
    """
    
    def __init__(self):
        """Initialise le système de métriques."""
        self.categorization_metrics = CategorizationMetrics()
        self.resolution_metrics = ResolutionMetrics()
        self.execution_time_metrics = ExecutionTimeMetrics()
        self.template_metrics = TemplateMetrics()
        
        # Métriques globales
        self.total_puzzles_processed = 0
        self.successful_puzzles = 0
        self.start_time = time.time()
        
    def record_pipeline_result(self, puzzle_data: PuzzleData, result: PipelineResult):
        """
        Enregistre le résultat d'un pipeline pour calcul des métriques.
        
        Args:
            puzzle_data: Données du puzzle traité
            result: Résultat du pipeline
        """
        self.total_puzzles_processed += 1
        
        if result.success:
            self.successful_puzzles += 1
        
        # Métriques de temps d'exécution
        self.execution_time_metrics.add_pipeline_time(result.execution_time_ms)
        
        # Métriques par étape
        if result.step1_analysis:
            self.execution_time_metrics.add_component_time(
                "pattern_analyzer", 
                result.step1_analysis.execution_time_ms
            )
        
        if result.step2_categorization:
            self.categorization_metrics.add_categorization(
                result.step2_categorization.category
            )
            
            # Métriques de template
            template_found = result.step3_template and result.step3_template.success
            self.template_metrics.add_template_usage(
                result.step2_categorization.category,
                template_found
            )
        
        if result.step4_solution:
            self.resolution_metrics.add_resolution_attempt(
                result.step4_solution.success,
                result.step4_solution.combinations_tested,
                "timeout" in (result.step4_solution.error or "").lower()
            )
    
    def calculate_overall_success_rate(self) -> float:
        """Calcule le taux de réussite global."""
        if self.total_puzzles_processed == 0:
            return 0.0
        return self.successful_puzzles / self.total_puzzles_processed
    
    def validate_30_percent_objective(self) -> Dict[str, Any]:
        """
        Valide l'objectif de 30% de puzzles résolus.
        
        Returns:
            Dict avec validation de l'objectif
        """
        success_rate = self.calculate_overall_success_rate()
        target_rate = 0.30
        
        return {
            'objective_met': success_rate >= target_rate,
            'current_success_rate': success_rate,
            'target_success_rate': target_rate,
            'puzzles_processed': self.total_puzzles_processed,
            'successful_puzzles': self.successful_puzzles,
            'gap_to_target': target_rate - success_rate,
            'method': 'algorithmic_success_counting',
            'note': 'Objectif évalué par comptage simple, pas d\'estimation IA'
        }
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """
        Génère un rapport complet avec transparence sur les méthodes.
        
        Returns:
            Dict avec toutes les métriques et leur méthode de calcul
        """
        runtime_hours = (time.time() - self.start_time) / 3600
        
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'runtime_hours': runtime_hours,
            'system_method': 'algorithmic_metrics_calculation',
            
            # Métriques globales
            'global_metrics': {
                'total_puzzles_processed': self.total_puzzles_processed,
                'successful_puzzles': self.successful_puzzles,
                'overall_success_rate': self.calculate_overall_success_rate(),
                'method': 'simple_counting'
            },
            
            # Validation de l'objectif
            'objective_validation': self.validate_30_percent_objective(),
            
            # Métriques détaillées
            'categorization_metrics': self.categorization_metrics.to_dict(),
            'resolution_metrics': self.resolution_metrics.to_dict(),
            'execution_time_metrics': self.execution_time_metrics.to_dict(),
            'template_metrics': self.template_metrics.to_dict(),
            
            # Transparence sur les méthodes
            'methodology_transparency': {
                'pattern_analysis': 'Calculs géométriques (rotations, symétries, histogrammes)',
                'categorization': 'Règles conditionnelles explicites',
                'template_generation': 'Base de données de templates pré-définis',
                'parameter_resolution': 'Recherche par force brute optimisée',
                'execution': 'Interpréteur de commandes AGI',
                'metrics_calculation': 'Statistiques descriptives simples',
                'no_ai_involved': 'Aucun apprentissage automatique ou IA utilisé'
            }
        }
        
        return report
    
    def save_report(self, filepath: str):
        """Sauvegarde le rapport dans un fichier JSON."""
        report = self.generate_comprehensive_report()
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
    
    def print_summary(self):
        """Affiche un résumé des métriques."""
        success_rate = self.calculate_overall_success_rate()
        objective_validation = self.validate_30_percent_objective()
        
        print("=" * 60)
        print("RAPPORT DE MÉTRIQUES HONNÊTES - PIPELINE ARC")
        print("=" * 60)
        print(f"Puzzles traités: {self.total_puzzles_processed}")
        print(f"Puzzles résolus: {self.successful_puzzles}")
        print(f"Taux de réussite: {success_rate:.1%}")
        print(f"Objectif 30%: {'✓ ATTEINT' if objective_validation['objective_met'] else '✗ NON ATTEINT'}")
        
        print("\n--- MÉTRIQUES DÉTAILLÉES ---")
        print(f"Précision catégorisation: {self.categorization_metrics.get_precision():.1%}")
        print(f"Taux de résolution paramètres: {self.resolution_metrics.get_resolution_rate():.1%}")
        print(f"Couverture templates: {self.template_metrics.get_coverage_rate():.1%}")
        print(f"Temps moyen pipeline: {self.execution_time_metrics.get_pipeline_average():.1f}ms")
        
        print("\n--- TRANSPARENCE MÉTHODOLOGIQUE ---")
        print("• Toutes les métriques sont calculées par des algorithmes explicites")
        print("• Aucun apprentissage automatique ou IA n'est utilisé")
        print("• Les calculs sont reproductibles et vérifiables")
        print("=" * 60)
    
    def get_performance_bottlenecks(self) -> List[Dict[str, Any]]:
        """Identifie les goulots d'étranglement de performance."""
        bottlenecks = []
        
        # Analyser les taux de réussite par étape
        if self.categorization_metrics.get_precision() < 0.8:
            bottlenecks.append({
                'component': 'categorization',
                'issue': 'Précision de catégorisation faible',
                'current_rate': self.categorization_metrics.get_precision(),
                'recommended_action': 'Améliorer les règles conditionnelles'
            })
        
        if self.resolution_metrics.get_resolution_rate() < 0.5:
            bottlenecks.append({
                'component': 'parameter_resolution',
                'issue': 'Taux de résolution de paramètres faible',
                'current_rate': self.resolution_metrics.get_resolution_rate(),
                'recommended_action': 'Optimiser la recherche ou augmenter le timeout'
            })
        
        if self.template_metrics.get_coverage_rate() < 0.7:
            bottlenecks.append({
                'component': 'template_coverage',
                'issue': 'Couverture de templates insuffisante',
                'current_rate': self.template_metrics.get_coverage_rate(),
                'recommended_action': 'Ajouter des templates pour les catégories manquantes'
            })
        
        # Analyser les temps d'exécution
        avg_time = self.execution_time_metrics.get_pipeline_average()
        if avg_time > 8000:  # Plus de 8 secondes
            bottlenecks.append({
                'component': 'execution_time',
                'issue': 'Temps d\'exécution trop élevé',
                'current_time_ms': avg_time,
                'recommended_action': 'Optimiser les algorithmes les plus lents'
            })
        
        return bottlenecks
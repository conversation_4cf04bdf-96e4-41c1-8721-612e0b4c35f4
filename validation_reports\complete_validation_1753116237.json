{"timestamp": 1753116236.7796464, "validation_type": "complete_system_validation", "components_tested": ["data_structures", "metrics_system", "performance_limits", "quality_metrics", "regression_tests"], "regression_tests": {"total_tests": 11, "passed_tests": 11, "failed_tests": 0, "success_rate": 100.0, "failed_test_names": [], "all_tests_passed": true, "detailed_results": [{"test_name": "Création PuzzleData", "status": "PASS", "error": null}, {"test_name": "Validation PuzzleData", "status": "PASS", "error": null}, {"test_name": "Création AnalysisResult", "status": "PASS", "error": null}, {"test_name": "Création TemplateResult", "status": "PASS", "error": null}, {"test_name": "Création SolutionResult", "status": "PASS", "error": null}, {"test_name": "Création PipelineResult", "status": "PASS", "error": null}, {"test_name": "ExecutionTimer", "status": "PASS", "error": null}, {"test_name": "Création résultats d'erreur", "status": "PASS", "error": null}, {"test_name": "MetricsCalculator de base", "status": "PASS", "error": null}, {"test_name": "Génération rapport métriques", "status": "PASS", "error": null}, {"test_name": "Valeurs de configuration", "status": "PASS", "error": null}]}, "performance_tests": {"timestamp": 1753116237.0177712, "test_puzzles_count": 5, "execution_time_tests": {"total_puzzles": 5, "within_time_limit": 5, "exceeded_time_limit": 0, "max_time_ms": 11.048316955566406, "min_time_ms": 10.232925415039062, "avg_time_ms": 10.571908950805664, "time_limit_ms": 10000, "details": [{"puzzle_id": "simple_rotation_90", "execution_time_ms": 11.048316955566406, "within_limit": true}, {"puzzle_id": "simple_fill", "execution_time_ms": 10.359764099121094, "within_limit": true}, {"puzzle_id": "simple_copy", "execution_time_ms": 10.581731796264648, "within_limit": true}, {"puzzle_id": "simple_mirror", "execution_time_ms": 10.232925415039062, "within_limit": true}, {"puzzle_id": "simple_color_change", "execution_time_ms": 10.63680648803711, "within_limit": true}]}, "quality_metrics_tests": {"metrics_report": {"categorization_accuracy": 20.0, "resolution_rate": 20.0, "average_execution_time_ms": 10.505247116088867, "template_coverage": 20.0, "category_breakdown": {"geometric_transform": {"total_puzzles": 2, "success_rate": 0.0, "failure_rate": 100.0, "timeout_rate": 0.0, "method_used": "algorithmic_pattern_detection"}, "color_pattern": {"total_puzzles": 2, "success_rate": 50.0, "failure_rate": 50.0, "timeout_rate": 0.0, "method_used": "algorithmic_pattern_detection"}, "copy_paste": {"total_puzzles": 1, "success_rate": 0.0, "failure_rate": 100.0, "timeout_rate": 0.0, "method_used": "algorithmic_pattern_detection"}}, "timeout_rate": 0.0, "error_rate": 80.0, "total_puzzles_tested": 5, "successful_resolutions": 1, "method_transparency": {"categorization": "geometric_calculations_and_histograms", "template_generation": "rule_based_substitution", "parameter_resolution": "brute_force_search", "validation": "direct_execution_comparison"}, "meets_target_resolution": false, "generation_timestamp": 1753116236.897867, "transparency_note": "Toutes les métriques sont calculées par des algorithmes statistiques explicites"}, "target_validation": {"target_percentage": 30.0, "current_percentage": 20.0, "meets_target": false, "gap": -10.0, "status": "NEEDS_IMPROVEMENT", "recommendation": "Améliorer les algorithmes pour gagner 10.0 points", "method_note": "Validation par calcul statistique direct, pas par évaluation IA"}, "quality_summary": {"resolution_rate": 20.0, "meets_30_percent_target": false, "categorization_accuracy": 20.0, "template_coverage": 20.0, "error_rate": 80.0, "timeout_rate": 0.0}}, "system_limits_tests": {"timeout_handling": {"puzzle_id": "timeout_test", "execution_time_ms": 11.020421981811523, "timeout_detected": false, "proper_error_handling": true}, "template_missing": {"puzzle_id": "unknown_pattern_test", "template_found": false, "proper_error_handling": true, "error_message_present": true}, "invalid_data": {"validation_failed": false, "error_caught": true, "error_message": "Grille 0 contient des valeurs invalides (hors 0-9)"}, "memory_limits": {"grid_size": 30, "memory_before_mb": 35.4140625, "memory_after_mb": 35.4140625, "memory_used_mb": 0.0, "within_memory_limit": true, "processing_successful": true}}, "overall_assessment": {"overall_success": false, "performance_grade": "PASS", "quality_grade": "FAIL", "robustness_grade": "PASS", "system_status": "NEEDS_IMPROVEMENT", "recommendations": ["Améliorer les templates et heuristiques pour atteindre 30% de résolution"]}, "transparency_note": "Tous les tests basés sur des calculs algorithmiques explicites"}}
"""
ScenarioGeneralizer pour la génération de templates.

Génération de templates par substitution de variables.
Base de données de patterns pré-définis, pas d'IA.
"""

import re
import numpy as np
from typing import Dict, List, Any, Tuple


class ScenarioGeneralizer:
    """
    Génération de templates par substitution de variables.
    Base de données de patterns pré-définis, pas d'IA.
    """
    
    def __init__(self):
        """Initialise le généralisateur avec les templates de base."""
        self.templates = self._load_templates()
        
    def _load_templates(self) -> Dict[str, str]:
        """
        Templates pré-définis par catégorie :
        {
            "color_fill": "FILL {color_var} [0,0 {width},{height}]",
            "rotation": "ROTATE {angle} {center}",
            "copy_pattern": "COPY {region}; PASTE {positions}",
            "symmetry": "MIRROR {axis} {region}",
            "resize": "RESIZE {scale_factor} {method}"
        }
        """
        templates = {
            "color_fill": "FILL {color_var} ([0,0 {width},{height}])",
            "rotation": "MOTIF {{ COPY ([0,0 {width},{height}]); ROTATE {angle}; PASTE ([0,0]) }}",
            "copy_pattern": "MOTIF {{ COPY ({region}); PASTE ({positions}) }}",
            "symmetry": "MOTIF {{ COPY ([0,0 {width},{height}]); FLIP {axis}; PASTE ([0,0]) }}",
            "resize": "RESIZE {new_size}",
            "geometric_transform": "MOTIF {{ COPY ([0,0 {width},{height}]); ROTATE {angle}; PASTE ([0,0]) }}",
            "color_pattern": "REPLACE {old_color} {new_color}",
            "resize_operation": "RESIZE {new_size}",
            "fill_pattern": "FILL {fill_color} ({fill_area})",
            "copy_paste": "MOTIF {{ COPY ({source_region}); PASTE ({target_positions}) }}",
            "complex_pattern": "REPLACE 0 1"
        }
        
        return templates
    
    def generalize_scenario(self, category: str, train_examples: List) -> Dict[str, Any]:
        """
        Algorithme de substitution :
        1. Sélectionner template selon catégorie
        2. Identifier valeurs variables
        3. Remplacer par placeholders
        4. Définir contraintes
        """
        # 1. Sélectionner template selon catégorie
        if category not in self.templates:
            print(f"ATTENTION: Catégorie inconnue '{category}', utilisation du template par défaut")
            category = "complex_pattern"
        
        template = self.templates[category]
        
        # 2. Identifier valeurs variables dans les exemples
        variables = self._extract_variables(template, train_examples)
        
        # 3. Le template contient déjà les placeholders
        
        # 4. Définir contraintes de résolution
        constraints = self._generate_constraints(template, variables, train_examples)
        
        return {
            'template': template,
            'variables': variables,
            'constraints': constraints,
            'category': category,
            'generation_method': 'rule_based'  # Pas "learned"
        }
    
    def _extract_variables(self, template: str, train_examples: List) -> Dict[str, List]:
        """Identification des valeurs variables dans les exemples"""
        variables = {}
        
        # Extraire les variables du template
        template_vars = re.findall(r'\{([^}]+)\}', template)
        
        for var in template_vars:
            if 'color' in var.lower():
                variables[var] = self._extract_color_values(train_examples)
            elif 'angle' in var.lower():
                variables[var] = [90, 180, 270]  # Angles possibles
            elif 'center' in var.lower():
                variables[var] = self._extract_center_positions(train_examples)
            elif 'size' in var.lower() or 'width' in var.lower() or 'height' in var.lower():
                variables[var] = self._extract_size_values(train_examples)
            elif 'region' in var.lower() or 'area' in var.lower():
                variables[var] = self._extract_regions(train_examples)
            elif 'axis' in var.lower():
                variables[var] = ['horizontal', 'vertical', 'diagonal']
            elif 'positions' in var.lower():
                variables[var] = self._extract_positions(train_examples)
            else:
                variables[var] = ['default_value']  # Valeur par défaut
        
        return variables
    
    def _extract_color_values(self, train_examples: List) -> List[int]:
        """Extrait les couleurs possibles des exemples"""
        colors = set()
        for input_grid, output_grid in train_examples:
            colors.update(np.unique(input_grid))
            colors.update(np.unique(output_grid))
        return sorted(list(colors))
    
    def _extract_center_positions(self, train_examples: List) -> List[str]:
        """Extrait les positions centrales possibles"""
        centers = []
        for input_grid, output_grid in train_examples:
            h, w = input_grid.shape
            center_x, center_y = w // 2, h // 2
            centers.append(f"[{center_x},{center_y}]")
        return list(set(centers))
    
    def _extract_size_values(self, train_examples: List) -> List[str]:
        """Extrait les valeurs de taille des exemples"""
        sizes = []
        for input_grid, output_grid in train_examples:
            h_in, w_in = input_grid.shape
            h_out, w_out = output_grid.shape
            sizes.extend([str(w_in), str(h_in), str(w_out), str(h_out)])
            sizes.extend([f"{w_in}x{h_in}", f"{w_out}x{h_out}"])
        return list(set(sizes))
    
    def _extract_regions(self, train_examples: List) -> List[str]:
        """Extrait les régions possibles des exemples"""
        regions = []
        for input_grid, output_grid in train_examples:
            h, w = input_grid.shape
            # Régions communes
            regions.extend([
                f"[0,0 {w},{h}]",  # Grille complète
                f"[0,0 {w//2},{h//2}]",  # Quart supérieur gauche
                f"[{w//2},0 {w},{h//2}]",  # Quart supérieur droit
                f"[0,{h//2} {w//2},{h}]",  # Quart inférieur gauche
                f"[{w//2},{h//2} {w},{h}]"  # Quart inférieur droit
            ])
        return list(set(regions))
    
    def _extract_positions(self, train_examples: List) -> List[str]:
        """Extrait les positions possibles des exemples"""
        positions = []
        for input_grid, output_grid in train_examples:
            h, w = input_grid.shape
            # Positions communes
            for i in range(0, h, max(1, h//4)):
                for j in range(0, w, max(1, w//4)):
                    positions.append(f"[{j},{i}]")
        return list(set(positions))
    
    def _generate_constraints(self, template: str, variables: Dict[str, List], 
                            train_examples: List) -> Dict[str, Any]:
        """Génère les contraintes de résolution pour les variables"""
        constraints = {}
        
        for var_name, possible_values in variables.items():
            if 'color' in var_name.lower():
                constraints[var_name] = {
                    'type': 'color_selection',
                    'method': 'most_frequent_in_output',
                    'possible_values': possible_values
                }
            elif 'angle' in var_name.lower():
                constraints[var_name] = {
                    'type': 'angle_detection',
                    'method': 'rotation_comparison',
                    'possible_values': possible_values
                }
            elif 'size' in var_name.lower():
                constraints[var_name] = {
                    'type': 'size_calculation',
                    'method': 'grid_dimensions',
                    'possible_values': possible_values
                }
            elif 'region' in var_name.lower() or 'area' in var_name.lower():
                constraints[var_name] = {
                    'type': 'region_detection',
                    'method': 'pattern_matching',
                    'possible_values': possible_values
                }
            elif 'axis' in var_name.lower():
                constraints[var_name] = {
                    'type': 'axis_detection',
                    'method': 'symmetry_analysis',
                    'possible_values': possible_values
                }
            else:
                constraints[var_name] = {
                    'type': 'generic',
                    'method': 'enumeration',
                    'possible_values': possible_values
                }
        
        return constraints
    
    def get_available_categories(self) -> List[str]:
        """Retourne la liste des catégories disponibles"""
        return list(self.templates.keys())
    
    def validate_template(self, template: str) -> Dict[str, Any]:
        """Valide la syntaxe d'un template"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Vérifier la syntaxe AGI de base
        if not self._is_valid_agi_syntax(template):
            validation_result['errors'].append("Syntaxe AGI invalide")
            validation_result['is_valid'] = False
        
        # Vérifier l'équilibrage des accolades
        brace_count = template.count('{') - template.count('}')
        if brace_count != 0:
            validation_result['errors'].append("Accolades non équilibrées")
            validation_result['is_valid'] = False
        
        return validation_result
    
    def _is_valid_agi_syntax(self, template: str) -> bool:
        """Vérifie la syntaxe AGI d'un template (basique)"""
        # Commandes AGI valides
        valid_commands = [
            'EDIT', 'FILL', 'CLEAR', 'REPLACE', 'COPY', 'PASTE', 'CUT',
            'FLIP', 'ROTATE', 'RESIZE', 'TRANSFERT', 'MOTIF', 'INIT',
            'MULTIPLY', 'DIVIDE', 'INSERT', 'DELETE', 'EXTRACT',
            'SURROUND', 'FLOODFILL', 'INVERT', 'COLOR', 'MIRROR'
        ]
        
        # Vérifier qu'au moins une commande valide est présente
        template_upper = template.upper()
        has_valid_command = any(cmd in template_upper for cmd in valid_commands)
        
        return has_valid_command
    
    def get_template_stats(self) -> Dict[str, Any]:
        """Retourne des statistiques sur les templates (pour debugging)"""
        return {
            'total_templates': len(self.templates),
            'categories': list(self.templates.keys()),
            'method': 'rule_based_template_database',
            'note': 'Templates pré-définis par catégorie, pas d\'apprentissage automatique'
        }
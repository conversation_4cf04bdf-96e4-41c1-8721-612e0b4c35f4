#!/usr/bin/env python3
"""
Tests unitaires pour les modèles de données et structures.

Valide les dataclasses, la gestion d'erreurs explicites,
et les constantes ERROR_MESSAGES avec descriptions transparentes.
"""

import sys
import os
sys.path.append('.')

import numpy as np
from models.data_structures import (
    PuzzleData, AnalysisResult, CategorizationResult, TemplateResult,
    SolutionResult, ExecutionResult, PipelineResult, PipelineConfig,
    ComponentStats, PipelineStats, PipelineStep, ErrorType,
    ERROR_MESSAGES, create_error_result
)


def test_puzzle_data():
    """Test de la dataclass PuzzleData."""
    print("=== Test PuzzleData ===")
    
    # Données valides
    train_examples = [
        (np.array([[1, 2], [3, 4]]), np.array([[5, 6], [7, 8]])),
        (np.array([[2, 3], [4, 5]]), np.array([[6, 7], [8, 9]]))
    ]
    test_input = np.array([[1, 1], [2, 2]])
    
    puzzle = PuzzleData(
        puzzle_id="test_001",
        train_examples=train_examples,
        test_input=test_input
    )
    
    # Vérifications de base
    assert puzzle.puzzle_id == "test_001", "Puzzle ID incorrect"
    assert len(puzzle.train_examples) == 2, "Nombre d'exemples incorrect"
    assert puzzle.test_input.shape == (2, 2), "Forme test input incorrecte"
    
    # Test des statistiques
    stats = puzzle.get_stats()
    assert stats['num_train_examples'] == 2, "Statistiques incorrectes"
    assert stats['test_input_shape'] == (2, 2), "Forme dans stats incorrecte"
    
    print("✓ PuzzleData avec données valides")
    
    # Test validation - exemples vides
    try:
        PuzzleData(
            puzzle_id="invalid",
            train_examples=[],
            test_input=test_input
        )
        assert False, "Devrait lever une exception pour exemples vides"
    except ValueError as e:
        assert "Au moins un exemple train est requis" in str(e)
        print("✓ Validation exemples vides")
    
    # Test validation - test input vide
    try:
        PuzzleData(
            puzzle_id="invalid",
            train_examples=train_examples,
            test_input=np.array([])
        )
        assert False, "Devrait lever une exception pour test input vide"
    except ValueError as e:
        assert "Test input ne peut pas être vide" in str(e)
        print("✓ Validation test input vide")
    
    return True


def test_analysis_result():
    """Test de la dataclass AnalysisResult."""
    print("\n=== Test AnalysisResult ===")
    
    # Résultat réussi
    analysis = AnalysisResult(
        success=True,
        common_patterns={
            'rotation_angles': [90],
            'color_changes': {1: 2}
        },
        execution_time_ms=15.5,
        method="geometric_calculations"
    )
    
    assert analysis.success == True, "Success doit être True"
    assert analysis.method == "geometric_calculations", "Méthode incorrecte"
    assert analysis.get_dominant_pattern() == "rotation", "Pattern dominant incorrect"
    
    print("✓ AnalysisResult réussi")
    
    # Résultat échoué
    failed_analysis = AnalysisResult(
        success=False,
        error="Calculs géométriques échoués"
    )
    
    assert failed_analysis.success == False, "Success doit être False"
    assert failed_analysis.get_dominant_pattern() is None, "Pattern dominant doit être None"
    
    print("✓ AnalysisResult échoué")
    
    return True


def test_categorization_result():
    """Test de la dataclass CategorizationResult."""
    print("\n=== Test CategorizationResult ===")
    
    # Catégorisation valide
    categorization = CategorizationResult(
        success=True,
        category="color_pattern",
        confidence=0.85,
        method="conditional_rules"
    )
    
    assert categorization.success == True, "Success doit être True"
    assert categorization.is_valid_category() == True, "Catégorie doit être valide"
    
    print("✓ CategorizationResult valide")
    
    # Catégorie invalide
    invalid_categorization = CategorizationResult(
        success=True,
        category="invalid_category"
    )
    
    assert invalid_categorization.is_valid_category() == False, "Catégorie doit être invalide"
    
    print("✓ Validation catégorie invalide")
    
    return True


def test_template_result():
    """Test de la dataclass TemplateResult."""
    print("\n=== Test TemplateResult ===")
    
    # Template valide
    template = TemplateResult(
        success=True,
        template="REPLACE {old_color} {new_color}",
        variables={
            'old_color': ['1', '2'],
            'new_color': ['3', '4']
        },
        category="color_pattern",
        method="template_substitution"
    )
    
    assert template.success == True, "Success doit être True"
    assert template.get_variable_count() == 2, "Nombre de variables incorrect"
    assert template.has_required_variables() == True, "Doit avoir des variables requises"
    
    print("✓ TemplateResult valide")
    
    # Template sans variables
    empty_template = TemplateResult(
        success=True,
        template="TRANSFERT",
        variables={}
    )
    
    assert empty_template.has_required_variables() == False, "Ne doit pas avoir de variables requises"
    
    print("✓ Template sans variables")
    
    return True


def test_solution_result():
    """Test de la dataclass SolutionResult."""
    print("\n=== Test SolutionResult ===")
    
    # Solution résolue
    solution = SolutionResult(
        success=True,
        resolved_scenario="REPLACE 1 2",
        original_template="REPLACE {old_color} {new_color}",
        parameter_values={'old_color': '1', 'new_color': '2'},
        validation_score=1.0,
        combinations_tested=5,
        method="brute_force_search"
    )
    
    assert solution.success == True, "Success doit être True"
    assert solution.is_fully_resolved() == True, "Doit être complètement résolu"
    assert solution.validation_score == 1.0, "Score de validation incorrect"
    
    print("✓ SolutionResult résolu")
    
    # Solution non résolue
    unresolved = SolutionResult(
        success=False,
        resolved_scenario="REPLACE {old_color} 2",
        error="Paramètre old_color non résolu"
    )
    
    assert unresolved.is_fully_resolved() == False, "Ne doit pas être résolu"
    
    print("✓ Solution non résolue")
    
    return True


def test_execution_result():
    """Test de la dataclass ExecutionResult."""
    print("\n=== Test ExecutionResult ===")
    
    # Exécution réussie
    output_grid = np.array([[2, 0, 2], [0, 2, 0], [2, 0, 2]])
    execution = ExecutionResult(
        success=True,
        output_grid=output_grid,
        executed_scenario="REPLACE 1 2",
        method="agi_interpreter"
    )
    
    assert execution.success == True, "Success doit être True"
    assert execution.get_output_shape() == (3, 3), "Forme de sortie incorrecte"
    assert execution.validate_output() == True, "Validation doit réussir"
    assert execution.validate_output(expected_shape=(3, 3)) == True, "Validation avec forme attendue doit réussir"
    
    print("✓ ExecutionResult réussi")
    
    # Exécution échouée
    failed_execution = ExecutionResult(
        success=False,
        error="Commande AGI invalide"
    )
    
    assert failed_execution.validate_output() == False, "Validation doit échouer"
    assert failed_execution.get_output_shape() is None, "Forme doit être None"
    
    print("✓ ExecutionResult échoué")
    
    return True


def test_pipeline_result():
    """Test de la dataclass PipelineResult."""
    print("\n=== Test PipelineResult ===")
    
    # Créer des résultats d'étapes
    analysis = AnalysisResult(success=True, method="geometric_calculations")
    categorization = CategorizationResult(success=True, category="color_pattern")
    template = TemplateResult(success=True, template="REPLACE {old_color} {new_color}")
    solution = SolutionResult(success=False, error="Aucune solution trouvée")
    
    # Pipeline avec échec à l'étape 4
    pipeline_result = PipelineResult(
        success=False,
        puzzle_id="test_001",
        execution_time_ms=150.0,
        error="Échec résolution paramètres",
        step1_analysis=analysis,
        step2_categorization=categorization,
        step3_template=template,
        step4_solution=solution
    )
    
    assert pipeline_result.success == False, "Success doit être False"
    assert pipeline_result.get_failed_step() == PipelineStep.PARAMETER_RESOLUTION, "Étape échouée incorrecte"
    assert pipeline_result.get_success_rate() == 0.75, "Taux de réussite incorrect (3/4)"
    
    print("✓ PipelineResult avec échec")
    
    # Test résumé
    summary = pipeline_result.to_summary()
    assert summary['success'] == False, "Résumé success incorrect"
    assert summary['failed_step'] == "parameter_resolution", "Étape échouée dans résumé incorrecte"
    assert summary['success_rate'] == 0.75, "Taux de réussite dans résumé incorrect"
    
    print("✓ Résumé PipelineResult")
    
    return True


def test_error_messages():
    """Test des constantes ERROR_MESSAGES."""
    print("\n=== Test ERROR_MESSAGES ===")
    
    # Vérifier que tous les types d'erreurs ont des messages
    for error_type in ErrorType:
        assert error_type in ERROR_MESSAGES, f"Message manquant pour {error_type}"
        
        error_info = ERROR_MESSAGES[error_type]
        assert 'message' in error_info, f"Message manquant pour {error_type}"
        assert 'description' in error_info, f"Description manquante pour {error_type}"
        assert 'method' in error_info, f"Méthode manquante pour {error_type}"
        
        # Vérifier la transparence des messages
        assert len(error_info['message']) > 10, f"Message trop court pour {error_type}"
        assert len(error_info['description']) > 20, f"Description trop courte pour {error_type}"
    
    print("✓ Tous les types d'erreurs ont des messages complets")
    
    # Test création d'erreur
    error_result = create_error_result(
        ErrorType.PATTERN_ANALYSIS_FAILED,
        PipelineStep.PATTERN_ANALYSIS,
        "Grille trop complexe"
    )
    
    assert error_result['success'] == False, "Résultat d'erreur doit être False"
    assert error_result['error_type'] == "pattern_analysis_failed", "Type d'erreur incorrect"
    assert error_result['step'] == "pattern_analysis", "Étape incorrecte"
    assert error_result['is_algorithmic'] == True, "Doit être marqué comme algorithmique"
    assert "Grille trop complexe" in error_result['additional_info'], "Info supplémentaire manquante"
    
    print("✓ Création d'erreur standardisée")
    
    return True


def test_pipeline_config():
    """Test de la dataclass PipelineConfig."""
    print("\n=== Test PipelineConfig ===")
    
    # Configuration valide
    config = PipelineConfig(
        timeout_per_puzzle=15,
        timeout_parameter_resolution=8,
        max_combinations_to_test=500,
        enable_logging=True,
        log_level="DEBUG"
    )
    
    assert config.validate() == True, "Configuration valide doit passer la validation"
    
    print("✓ Configuration valide")
    
    # Configuration invalide
    invalid_config = PipelineConfig(
        timeout_per_puzzle=0,  # Invalide
        timeout_parameter_resolution=-1  # Invalide
    )
    
    assert invalid_config.validate() == False, "Configuration invalide doit échouer la validation"
    
    print("✓ Validation configuration invalide")
    
    return True


def test_component_stats():
    """Test de la dataclass ComponentStats."""
    print("\n=== Test ComponentStats ===")
    
    stats = ComponentStats(
        component_name="PatternAnalyzer",
        method="geometric_calculations"
    )
    
    # État initial
    assert stats.get_success_rate() == 0.0, "Taux initial doit être 0"
    
    # Mise à jour avec succès
    stats.update_stats(success=True, execution_time_ms=10.0)
    stats.update_stats(success=True, execution_time_ms=20.0)
    stats.update_stats(success=False, execution_time_ms=15.0, error="Erreur test")
    
    assert stats.total_calls == 3, "Nombre total d'appels incorrect"
    assert stats.successful_calls == 2, "Nombre d'appels réussis incorrect"
    assert stats.get_success_rate() == 2/3, "Taux de réussite incorrect"
    assert stats.average_execution_time_ms == 15.0, "Temps moyen incorrect"
    assert stats.last_error == "Erreur test", "Dernière erreur incorrecte"
    
    print("✓ ComponentStats avec mises à jour")
    
    return True


def test_pipeline_stats():
    """Test de la dataclass PipelineStats."""
    print("\n=== Test PipelineStats ===")
    
    pipeline_stats = PipelineStats()
    
    # Ajouter des statistiques de composants
    analyzer_stats = pipeline_stats.add_component_stats("PatternAnalyzer", "geometric_calculations")
    resolver_stats = pipeline_stats.add_component_stats("ParameterResolver", "brute_force_search")
    
    assert len(pipeline_stats.component_stats) == 2, "Nombre de composants incorrect"
    
    # Mettre à jour les statistiques globales
    pipeline_stats.update_global_stats(success=True, execution_time_ms=100.0)
    pipeline_stats.update_global_stats(success=False, execution_time_ms=200.0)
    pipeline_stats.update_global_stats(success=True, execution_time_ms=150.0)
    
    assert pipeline_stats.total_puzzles_processed == 3, "Nombre total de puzzles incorrect"
    assert pipeline_stats.successful_puzzles == 2, "Nombre de puzzles réussis incorrect"
    assert pipeline_stats.get_success_rate() == 2/3, "Taux de réussite global incorrect"
    assert pipeline_stats.average_execution_time_ms == 150.0, "Temps moyen global incorrect"
    
    print("✓ PipelineStats avec mises à jour")
    
    # Test conversion en dictionnaire
    stats_dict = pipeline_stats.to_dict()
    
    assert 'global' in stats_dict, "Section global manquante"
    assert 'components' in stats_dict, "Section components manquante"
    assert stats_dict['global']['success_rate'] == 2/3, "Taux de réussite dans dict incorrect"
    assert 'PatternAnalyzer' in stats_dict['components'], "Composant PatternAnalyzer manquant"
    
    print("✓ Conversion en dictionnaire")
    
    return True


def test_requirements_compliance():
    """Vérifie la conformité aux requirements."""
    print("\n=== Vérification des Requirements ===")
    
    # Requirement 7.5: Dataclasses pour structurer les données
    puzzle = PuzzleData("test", [(np.array([[1]]), np.array([[2]]))], np.array([[3]]))
    assert hasattr(puzzle, 'puzzle_id'), "PuzzleData doit avoir puzzle_id"
    assert hasattr(puzzle, 'train_examples'), "PuzzleData doit avoir train_examples"
    print("✓ Requirement 7.5: Dataclasses implémentées")
    
    # Gestion d'erreurs explicites
    error_result = create_error_result(ErrorType.TIMEOUT_EXCEEDED, PipelineStep.EXECUTION)
    assert error_result['success'] == False, "Erreur doit indiquer échec"
    assert 'message' in error_result, "Erreur doit avoir un message"
    assert 'description' in error_result, "Erreur doit avoir une description"
    print("✓ Gestion d'erreurs explicites")
    
    # Messages honnêtes avec descriptions transparentes
    for error_type in ErrorType:
        error_info = ERROR_MESSAGES[error_type]
        # Vérifier que les messages sont honnêtes (pas de promesses d'IA)
        assert 'apprentissage' not in error_info['message'].lower(), f"Message {error_type} ne doit pas mentionner l'apprentissage"
        assert 'ia' not in error_info['message'].lower(), f"Message {error_type} ne doit pas mentionner l'IA"
        assert 'intelligence artificielle' not in error_info['message'].lower(), f"Message {error_type} ne doit pas mentionner l'IA"
    print("✓ Messages honnêtes sans références à l'IA")
    
    # Structures de configuration
    config = PipelineConfig()
    assert hasattr(config, 'timeout_per_puzzle'), "Config doit avoir timeout_per_puzzle"
    assert config.validate(), "Configuration par défaut doit être valide"
    print("✓ Structures de configuration")
    
    return True


def main():
    """Fonction principale de test."""
    print("Tests unitaires des modèles de données et structures")
    print("=" * 60)
    
    success = True
    
    try:
        success &= test_puzzle_data()
        success &= test_analysis_result()
        success &= test_categorization_result()
        success &= test_template_result()
        success &= test_solution_result()
        success &= test_execution_result()
        success &= test_pipeline_result()
        success &= test_error_messages()
        success &= test_pipeline_config()
        success &= test_component_stats()
        success &= test_pipeline_stats()
        success &= test_requirements_compliance()
        
        if success:
            print("\n🎉 Tous les tests des structures de données sont passés!")
            print("Modèles de données et gestion d'erreurs validés")
        else:
            print("\n❌ Certains tests ont échoué")
            
    except Exception as e:
        print(f"\n💥 Erreur durant les tests: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
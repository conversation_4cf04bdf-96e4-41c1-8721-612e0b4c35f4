"""
Tests unitaires pour les modèles de données et structures du pipeline ARC-Solver.

Valide que toutes les structures de données respectent les contraintes
et que la gestion d'erreurs fonctionne correctement.
"""

import pytest
import numpy as np
from src.data_structures import (
    PuzzleData, AnalysisResult, TemplateResult, SolutionResult, PipelineResult,
    ERROR_MESSAGES, PipelineConfig, ExecutionTimer,
    create_error_result, validate_puzzle_data
)


class TestPuzzleData:
    """Tests pour la structure PuzzleData."""
    
    def test_valid_puzzle_data(self):
        """Test de création d'un PuzzleData valide."""
        train_inputs = [np.array([[1, 2], [3, 4]], dtype=np.int32)]
        train_outputs = [np.array([[2, 1], [4, 3]], dtype=np.int32)]
        test_input = np.array([[5, 6], [7, 8]], dtype=np.int32)
        
        puzzle = PuzzleData(
            puzzle_id="test_001",
            train_inputs=train_inputs,
            train_outputs=train_outputs,
            test_input=test_input
        )
        
        assert puzzle.puzzle_id == "test_001"
        assert len(puzzle.train_inputs) == 1
        assert len(puzzle.train_outputs) == 1
        assert puzzle.test_input.shape == (2, 2)
    
    def test_mismatched_train_data(self):
        """Test d'erreur avec nombre d'inputs != outputs."""
        train_inputs = [np.array([[1, 2]]), np.array([[3, 4]])]
        train_outputs = [np.array([[2, 1]])]  # Un seul output pour deux inputs
        test_input = np.array([[5, 6]])
        
        with pytest.raises(ValueError, match="Nombre d'inputs.*!= nombre d'outputs"):
            PuzzleData(
                puzzle_id="test_error",
                train_inputs=train_inputs,
                train_outputs=train_outputs,
                test_input=test_input
            )
    
    def test_empty_train_data(self):
        """Test d'erreur avec aucun exemple d'entraînement."""
        with pytest.raises(ValueError, match="Aucun exemple d'entraînement"):
            PuzzleData(
                puzzle_id="test_empty",
                train_inputs=[],
                train_outputs=[],
                test_input=np.array([[1, 2]])
            )


class TestAnalysisResult:
    """Tests pour la structure AnalysisResult."""
    
    def test_valid_analysis_result(self):
        """Test de création d'un AnalysisResult valide."""
        result = AnalysisResult(
            category="geometric_transform",
            confidence=0.85,
            detected_transformations=["rotation_90"],
            analysis_details={"rotation_angle": 90},
            execution_time_ms=150.5
        )
        
        assert result.category == "geometric_transform"
        assert result.confidence == 0.85
        assert "rotation_90" in result.detected_transformations
    
    def test_invalid_category(self):
        """Test d'erreur avec catégorie invalide."""
        with pytest.raises(ValueError, match="Catégorie invalide"):
            AnalysisResult(
                category="invalid_category",
                confidence=0.5,
                detected_transformations=[],
                analysis_details={},
                execution_time_ms=100.0
            )
    
    def test_invalid_confidence(self):
        """Test d'erreur avec confidence hors limites."""
        with pytest.raises(ValueError, match="Confidence doit être entre 0.0 et 1.0"):
            AnalysisResult(
                category="color_pattern",
                confidence=1.5,  # Invalide
                detected_transformations=[],
                analysis_details={},
                execution_time_ms=100.0
            )


class TestTemplateResult:
    """Tests pour la structure TemplateResult."""
    
    def test_valid_template_result(self):
        """Test de création d'un TemplateResult valide."""
        result = TemplateResult(
            template="FILL {color_var} [0,0 {width},{height}]",
            variables={"color_var": [1, 2, 3], "width": [10], "height": [10]},
            constraints={"color_var": "must_be_present_in_input"},
            generation_method="rule_based",
            category_used="color_fill"
        )
        
        assert result.template.startswith("FILL")
        assert result.generation_method == "rule_based"
        assert "color_var" in result.variables
    
    def test_invalid_generation_method(self):
        """Test d'erreur avec méthode de génération invalide."""
        with pytest.raises(ValueError, match="Seule la méthode 'rule_based'"):
            TemplateResult(
                template="FILL {color} [0,0 10,10]",
                variables={},
                constraints={},
                generation_method="ai_learned",  # Invalide
                category_used="color_fill"
            )
    
    def test_empty_template(self):
        """Test d'erreur avec template vide."""
        with pytest.raises(ValueError, match="Template ne peut pas être vide"):
            TemplateResult(
                template="",  # Vide
                variables={},
                constraints={},
                generation_method="rule_based",
                category_used="color_fill"
            )


class TestSolutionResult:
    """Tests pour la structure SolutionResult."""
    
    def test_valid_solution_result(self):
        """Test de création d'un SolutionResult valide."""
        result = SolutionResult(
            success=True,
            solution_command="FILL 1 [0,0 10,10]",
            execution_time_ms=2500.0,
            validation_score=0.95,
            error_message=None,
            method_used="algorithmic_search",
            parameters_tested=150,
            timeout_reached=False
        )
        
        assert result.success is True
        assert result.solution_command == "FILL 1 [0,0 10,10]"
        assert result.method_used == "algorithmic_search"
    
    def test_invalid_method(self):
        """Test d'erreur avec méthode invalide."""
        with pytest.raises(ValueError, match="Seule la méthode 'algorithmic_search'"):
            SolutionResult(
                success=True,
                solution_command="FILL 1 [0,0 10,10]",
                execution_time_ms=1000.0,
                validation_score=0.8,
                error_message=None,
                method_used="ai_inference",  # Invalide
                parameters_tested=100,
                timeout_reached=False
            )
    
    def test_success_without_command(self):
        """Test d'erreur avec succès mais pas de commande."""
        with pytest.raises(ValueError, match="Solution réussie mais commande manquante"):
            SolutionResult(
                success=True,
                solution_command=None,  # Manquante
                execution_time_ms=1000.0,
                validation_score=0.8,
                error_message=None,
                method_used="algorithmic_search",
                parameters_tested=100,
                timeout_reached=False
            )


class TestPipelineResult:
    """Tests pour la structure PipelineResult."""
    
    def test_execution_summary(self):
        """Test du résumé d'exécution."""
        analysis = AnalysisResult(
            category="geometric_transform",
            confidence=0.8,
            detected_transformations=["rotation"],
            analysis_details={},
            execution_time_ms=100.0
        )
        
        template = TemplateResult(
            template="ROTATE {angle} {center}",
            variables={"angle": [90], "center": ["5,5"]},
            constraints={},
            generation_method="rule_based",
            category_used="geometric_transform"
        )
        
        solution = SolutionResult(
            success=True,
            solution_command="ROTATE 90 5,5",
            execution_time_ms=1000.0,
            validation_score=1.0,
            error_message=None,
            method_used="algorithmic_search",
            parameters_tested=50,
            timeout_reached=False
        )
        
        pipeline_result = PipelineResult(
            puzzle_id="test_pipeline",
            success=True,
            final_solution="ROTATE 90 5,5",
            analysis_result=analysis,
            template_result=template,
            solution_result=solution,
            total_execution_time_ms=1200.0,
            error_step=None,
            error_message=None
        )
        
        summary = pipeline_result.get_execution_summary()
        
        assert summary["puzzle_id"] == "test_pipeline"
        assert summary["success"] is True
        assert summary["analysis_method"] == "geometric_calculations"
        assert summary["template_method"] == "rule_based_substitution"
        assert summary["resolution_method"] == "algorithmic_search"
        assert "algorithmes programmés" in summary["transparency_note"]


class TestExecutionTimer:
    """Tests pour l'utilitaire ExecutionTimer."""
    
    def test_timer_basic_usage(self):
        """Test d'utilisation basique du timer."""
        timer = ExecutionTimer()
        timer.start()
        
        # Simuler une petite pause
        import time
        time.sleep(0.01)  # 10ms
        
        elapsed = timer.stop()
        assert elapsed >= 10.0  # Au moins 10ms
        assert elapsed < 100.0  # Mais pas trop
    
    def test_timer_not_started(self):
        """Test d'erreur avec timer non démarré."""
        timer = ExecutionTimer()
        
        with pytest.raises(ValueError, match="Timer non démarré"):
            timer.stop()
        
        with pytest.raises(ValueError, match="Timer non démarré"):
            timer.get_elapsed_ms()


class TestErrorHandling:
    """Tests pour la gestion d'erreurs."""
    
    def test_create_error_result(self):
        """Test de création d'un résultat d'erreur."""
        result = create_error_result(
            error_type="no_pattern_detected",
            puzzle_id="test_error",
            execution_time_ms=500.0
        )
        
        assert result.success is False
        assert result.puzzle_id == "test_error"
        assert result.error_step == "no_pattern_detected"
        assert "algorithmes programmés" in result.error_message
    
    def test_error_messages_exist(self):
        """Test que tous les messages d'erreur sont définis."""
        required_errors = [
            "no_pattern_detected", "template_not_found", "parameter_search_failed",
            "timeout_exceeded", "execution_failed", "json_corrupted",
            "grid_size_mismatch", "invalid_command", "validation_failed"
        ]
        
        for error_type in required_errors:
            assert error_type in ERROR_MESSAGES
            assert len(ERROR_MESSAGES[error_type]) > 0


class TestValidation:
    """Tests pour la validation des données."""
    
    def test_validate_valid_puzzle(self):
        """Test de validation d'un puzzle valide."""
        puzzle = PuzzleData(
            puzzle_id="valid_test",
            train_inputs=[np.array([[1, 2], [3, 4]], dtype=np.int32)],
            train_outputs=[np.array([[2, 1], [4, 3]], dtype=np.int32)],
            test_input=np.array([[5, 6], [7, 8]], dtype=np.int32)
        )
        
        assert validate_puzzle_data(puzzle) is True
    
    def test_validate_invalid_values(self):
        """Test de validation avec valeurs invalides."""
        puzzle = PuzzleData(
            puzzle_id="invalid_test",
            train_inputs=[np.array([[1, 15], [3, 4]], dtype=np.int32)],  # 15 > 9
            train_outputs=[np.array([[2, 1], [4, 3]], dtype=np.int32)],
            test_input=np.array([[5, 6], [7, 8]], dtype=np.int32)
        )
        
        with pytest.raises(ValueError, match="contient des valeurs invalides"):
            validate_puzzle_data(puzzle)
    
    def test_validate_wrong_type(self):
        """Test de validation avec mauvais type."""
        puzzle = PuzzleData(
            puzzle_id="wrong_type_test",
            train_inputs=[[[1, 2], [3, 4]]],  # Liste au lieu de numpy array
            train_outputs=[np.array([[2, 1], [4, 3]], dtype=np.int32)],
            test_input=np.array([[5, 6], [7, 8]], dtype=np.int32)
        )
        
        with pytest.raises(ValueError, match="n'est pas un numpy array"):
            validate_puzzle_data(puzzle)


class TestPipelineConfig:
    """Tests pour la configuration du pipeline."""
    
    def test_config_values(self):
        """Test des valeurs de configuration."""
        assert PipelineConfig.PATTERN_ANALYSIS_TIMEOUT == 2
        assert PipelineConfig.PARAMETER_RESOLUTION_TIMEOUT == 10
        assert PipelineConfig.TOTAL_PIPELINE_TIMEOUT == 15
        assert PipelineConfig.TARGET_RESOLUTION_RATE == 0.30
        assert PipelineConfig.MAX_EXECUTION_TIME_MS == 10000


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
"""
Suite de tests de régression pour le pipeline ARC-Solver.

Évite les régressions en validant que les composants continuent
de fonctionner correctement après les modifications.
Tous les tests sont basés sur des calculs algorithmiques explicites.
"""

import sys
import time
import numpy as np
from typing import List, Dict, Any

# Ajouter le répertoire src au path
sys.path.append('src')

from data_structures import (
    PuzzleData, PipelineResult, AnalysisResult, TemplateResult, SolutionResult,
    PipelineConfig, ExecutionTimer, validate_puzzle_data, create_error_result
)
from metrics_system import MetricsCalculator, MetricsReporter


class RegressionTestSuite:
    """
    Suite de tests de régression pour éviter les régressions.
    
    Valide que les fonctionnalités de base continuent de fonctionner
    après les modifications du code.
    """
    
    def __init__(self):
        self.test_results: List[Dict[str, Any]] = []
        self.failed_tests: List[str] = []
    
    def run_test(self, test_name: str, test_func) -> bool:
        """
        Exécuter un test et enregistrer le résultat.
        
        Args:
            test_name: Nom du test
            test_func: Fonction de test à exécuter
        
        Returns:
            True si le test réussit
        """
        try:
            start_time = time.time()
            test_func()
            end_time = time.time()
            
            self.test_results.append({
                "test_name": test_name,
                "status": "PASS",
                "execution_time_ms": (end_time - start_time) * 1000,
                "error": None
            })
            print(f"✓ {test_name}")
            return True
            
        except Exception as e:
            end_time = time.time()
            self.test_results.append({
                "test_name": test_name,
                "status": "FAIL",
                "execution_time_ms": (end_time - start_time) * 1000,
                "error": str(e)
            })
            self.failed_tests.append(test_name)
            print(f"❌ {test_name}: {e}")
            return False
    
    def test_puzzle_data_creation(self):
        """Test de régression pour la création de PuzzleData."""
        # Test basique
        train_inputs = [np.array([[1, 2], [3, 4]], dtype=np.int32)]
        train_outputs = [np.array([[2, 1], [4, 3]], dtype=np.int32)]
        test_input = np.array([[5, 6], [7, 8]], dtype=np.int32)
        
        puzzle = PuzzleData(
            puzzle_id="regression_test_001",
            train_inputs=train_inputs,
            train_outputs=train_outputs,
            test_input=test_input
        )
        
        assert puzzle.puzzle_id == "regression_test_001"
        assert len(puzzle.train_inputs) == 1
        assert len(puzzle.train_outputs) == 1
        assert puzzle.test_input.shape == (2, 2)
        
        # Test de validation
        assert validate_puzzle_data(puzzle) is True
    
    def test_analysis_result_creation(self):
        """Test de régression pour AnalysisResult."""
        result = AnalysisResult(
            category="geometric_transform",
            confidence=0.85,
            detected_transformations=["rotation_90"],
            analysis_details={"rotation_angle": 90},
            execution_time_ms=150.5
        )
        
        assert result.category == "geometric_transform"
        assert result.confidence == 0.85
        assert "rotation_90" in result.detected_transformations
        assert result.execution_time_ms == 150.5
    
    def test_template_result_creation(self):
        """Test de régression pour TemplateResult."""
        result = TemplateResult(
            template="FILL {color_var} [0,0 {width},{height}]",
            variables={"color_var": [1, 2, 3], "width": [10], "height": [10]},
            constraints={"color_var": "must_be_present_in_input"},
            generation_method="rule_based",
            category_used="color_fill"
        )
        
        assert result.template.startswith("FILL")
        assert result.generation_method == "rule_based"
        assert "color_var" in result.variables
        assert result.category_used == "color_fill"
    
    def test_solution_result_creation(self):
        """Test de régression pour SolutionResult."""
        result = SolutionResult(
            success=True,
            solution_command="FILL 1 [0,0 10,10]",
            execution_time_ms=2500.0,
            validation_score=0.95,
            error_message=None,
            method_used="algorithmic_search",
            parameters_tested=150,
            timeout_reached=False
        )
        
        assert result.success is True
        assert result.solution_command == "FILL 1 [0,0 10,10]"
        assert result.method_used == "algorithmic_search"
        assert result.validation_score == 0.95
        assert result.parameters_tested == 150
    
    def test_pipeline_result_creation(self):
        """Test de régression pour PipelineResult."""
        analysis = AnalysisResult(
            category="geometric_transform",
            confidence=0.8,
            detected_transformations=["rotation"],
            analysis_details={},
            execution_time_ms=100.0
        )
        
        template = TemplateResult(
            template="ROTATE {angle} {center}",
            variables={"angle": [90], "center": ["5,5"]},
            constraints={},
            generation_method="rule_based",
            category_used="geometric_transform"
        )
        
        solution = SolutionResult(
            success=True,
            solution_command="ROTATE 90 5,5",
            execution_time_ms=1000.0,
            validation_score=1.0,
            error_message=None,
            method_used="algorithmic_search",
            parameters_tested=50,
            timeout_reached=False
        )
        
        pipeline_result = PipelineResult(
            puzzle_id="regression_pipeline_test",
            success=True,
            final_solution="ROTATE 90 5,5",
            analysis_result=analysis,
            template_result=template,
            solution_result=solution,
            total_execution_time_ms=1200.0,
            error_step=None,
            error_message=None
        )
        
        assert pipeline_result.puzzle_id == "regression_pipeline_test"
        assert pipeline_result.success is True
        assert pipeline_result.final_solution == "ROTATE 90 5,5"
        
        # Test du résumé d'exécution
        summary = pipeline_result.get_execution_summary()
        assert summary["success"] is True
        assert "algorithmes programmés" in summary["transparency_note"]
    
    def test_execution_timer(self):
        """Test de régression pour ExecutionTimer."""
        timer = ExecutionTimer()
        timer.start()
        
        # Simuler une petite pause
        time.sleep(0.01)  # 10ms
        
        elapsed = timer.stop()
        assert elapsed >= 10.0  # Au moins 10ms
        assert elapsed < 100.0  # Mais pas trop
        
        # Test de get_elapsed_ms pendant l'exécution
        timer2 = ExecutionTimer()
        timer2.start()
        time.sleep(0.005)  # 5ms
        elapsed_during = timer2.get_elapsed_ms()
        assert elapsed_during >= 5.0
    
    def test_error_result_creation(self):
        """Test de régression pour create_error_result."""
        error_result = create_error_result(
            error_type="no_pattern_detected",
            puzzle_id="regression_error_test",
            execution_time_ms=500.0
        )
        
        assert error_result.success is False
        assert error_result.puzzle_id == "regression_error_test"
        assert error_result.error_step == "no_pattern_detected"
        assert error_result.total_execution_time_ms == 500.0
        assert error_result.error_message is not None
    
    def test_metrics_calculator_basic(self):
        """Test de régression pour MetricsCalculator."""
        calculator = MetricsCalculator()
        
        # Test avec calculateur vide
        assert calculator.calculate_categorization_accuracy() == 0.0
        assert calculator.calculate_resolution_rate() == 0.0
        assert calculator.calculate_average_execution_time() == 0.0
        
        # Ajouter un résultat de test
        analysis = AnalysisResult(
            category="geometric_transform",
            confidence=0.8,
            detected_transformations=["rotation"],
            analysis_details={},
            execution_time_ms=100.0
        )
        
        template = TemplateResult(
            template="ROTATE {angle} {center}",
            variables={"angle": [90]},
            constraints={},
            generation_method="rule_based",
            category_used="geometric_transform"
        )
        
        solution = SolutionResult(
            success=True,
            solution_command="ROTATE 90 5,5",
            execution_time_ms=1000.0,
            validation_score=1.0,
            error_message=None,
            method_used="algorithmic_search",
            parameters_tested=50,
            timeout_reached=False
        )
        
        result = PipelineResult(
            puzzle_id="metrics_test",
            success=True,
            final_solution="ROTATE 90 5,5",
            analysis_result=analysis,
            template_result=template,
            solution_result=solution,
            total_execution_time_ms=1200.0,
            error_step=None,
            error_message=None
        )
        
        calculator.add_result(result)
        
        # Vérifier les calculs
        assert calculator.calculate_resolution_rate() == 100.0
        assert calculator.calculate_categorization_accuracy() == 100.0
        assert calculator.calculate_average_execution_time() == 1200.0
        assert calculator.calculate_template_coverage() == 100.0
    
    def test_metrics_report_generation(self):
        """Test de régression pour la génération de rapport."""
        calculator = MetricsCalculator()
        
        # Ajouter quelques résultats
        for i in range(3):
            analysis = AnalysisResult(
                category="geometric_transform",
                confidence=0.8,
                detected_transformations=["rotation"],
                analysis_details={},
                execution_time_ms=100.0
            )
            
            template = TemplateResult(
                template=f"ROTATE {{angle}} {{center}}",
                variables={"angle": [90]},
                constraints={},
                generation_method="rule_based",
                category_used="geometric_transform"
            )
            
            solution = SolutionResult(
                success=True,
                solution_command=f"ROTATE 90 5,5",
                execution_time_ms=1000.0,
                validation_score=1.0,
                error_message=None,
                method_used="algorithmic_search",
                parameters_tested=50,
                timeout_reached=False
            )
            
            result = PipelineResult(
                puzzle_id=f"report_test_{i}",
                success=True,
                final_solution=f"ROTATE 90 5,5",
                analysis_result=analysis,
                template_result=template,
                solution_result=solution,
                total_execution_time_ms=1200.0,
                error_step=None,
                error_message=None
            )
            
            calculator.add_result(result)
        
        # Générer le rapport
        report = calculator.generate_report()
        
        assert report.total_puzzles_tested == 3
        assert report.successful_resolutions == 3
        assert report.resolution_rate == 100.0
        assert report.meets_target_resolution is True
        
        # Vérifier la transparence
        assert "brute_force_search" in report.method_transparency["parameter_resolution"]
        assert "rule_based_substitution" in report.method_transparency["template_generation"]
    
    def test_config_values(self):
        """Test de régression pour les valeurs de configuration."""
        assert PipelineConfig.PATTERN_ANALYSIS_TIMEOUT == 2
        assert PipelineConfig.PARAMETER_RESOLUTION_TIMEOUT == 10
        assert PipelineConfig.TOTAL_PIPELINE_TIMEOUT == 15
        assert PipelineConfig.TARGET_RESOLUTION_RATE == 0.30
        assert PipelineConfig.MAX_EXECUTION_TIME_MS == 10000
        assert PipelineConfig.MAX_GRID_SIZE == 30
        assert PipelineConfig.MIN_VALIDATION_SCORE == 0.8
    
    def test_data_validation_edge_cases(self):
        """Test de régression pour les cas limites de validation."""
        # Test avec grilles de taille 1x1
        small_puzzle = PuzzleData(
            puzzle_id="small_test",
            train_inputs=[np.array([[1]], dtype=np.int32)],
            train_outputs=[np.array([[2]], dtype=np.int32)],
            test_input=np.array([[3]], dtype=np.int32)
        )
        assert validate_puzzle_data(small_puzzle) is True
        
        # Test avec grilles de taille maximale
        max_size = PipelineConfig.MAX_GRID_SIZE
        large_grid = np.zeros((max_size, max_size), dtype=np.int32)
        large_puzzle = PuzzleData(
            puzzle_id="large_test",
            train_inputs=[large_grid],
            train_outputs=[large_grid],
            test_input=large_grid
        )
        assert validate_puzzle_data(large_puzzle) is True
        
        # Test avec toutes les couleurs valides (0-9)
        color_grid = np.array([[0, 1, 2, 3, 4], [5, 6, 7, 8, 9]], dtype=np.int32)
        color_puzzle = PuzzleData(
            puzzle_id="color_test",
            train_inputs=[color_grid],
            train_outputs=[color_grid],
            test_input=color_grid
        )
        assert validate_puzzle_data(color_puzzle) is True
    
    def run_all_tests(self) -> Dict[str, Any]:
        """
        Exécuter tous les tests de régression.
        
        Returns:
            Résultats complets des tests
        """
        print("=== SUITE DE TESTS DE RÉGRESSION ===")
        print("Validation que les fonctionnalités de base fonctionnent toujours")
        
        # Liste des tests à exécuter
        tests = [
            ("Création PuzzleData", self.test_puzzle_data_creation),
            ("Création AnalysisResult", self.test_analysis_result_creation),
            ("Création TemplateResult", self.test_template_result_creation),
            ("Création SolutionResult", self.test_solution_result_creation),
            ("Création PipelineResult", self.test_pipeline_result_creation),
            ("ExecutionTimer", self.test_execution_timer),
            ("Création résultat d'erreur", self.test_error_result_creation),
            ("MetricsCalculator basique", self.test_metrics_calculator_basic),
            ("Génération rapport métriques", self.test_metrics_report_generation),
            ("Valeurs de configuration", self.test_config_values),
            ("Validation cas limites", self.test_data_validation_edge_cases)
        ]
        
        # Exécuter tous les tests
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            if self.run_test(test_name, test_func):
                passed += 1
        
        # Résumé des résultats
        success_rate = (passed / total) * 100 if total > 0 else 0
        
        results = {
            "timestamp": time.time(),
            "total_tests": total,
            "passed_tests": passed,
            "failed_tests": len(self.failed_tests),
            "success_rate": success_rate,
            "failed_test_names": self.failed_tests,
            "detailed_results": self.test_results,
            "overall_status": "PASS" if passed == total else "FAIL",
            "regression_detected": len(self.failed_tests) > 0
        }
        
        print(f"\n=== RÉSULTATS DES TESTS DE RÉGRESSION ===")
        print(f"Tests réussis: {passed}/{total} ({success_rate:.1f}%)")
        print(f"Statut global: {results['overall_status']}")
        
        if self.failed_tests:
            print(f"Tests échoués:")
            for test_name in self.failed_tests:
                print(f"  • {test_name}")
            print(f"⚠️  RÉGRESSION DÉTECTÉE - Vérifier les modifications récentes")
        else:
            print(f"✅ AUCUNE RÉGRESSION DÉTECTÉE - Toutes les fonctionnalités fonctionnent")
        
        return results


def main():
    """Exécuter la suite de tests de régression."""
    suite = RegressionTestSuite()
    
    try:
        results = suite.run_all_tests()
        return results["overall_status"] == "PASS"
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DES TESTS DE RÉGRESSION: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
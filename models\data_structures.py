"""
Modèles de données et structures pour le pipeline ARC.

Dataclasses pour structurer les données entre les composants.
Gestion d'erreurs explicites avec messages honnêtes.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
from enum import Enum


class PipelineStep(Enum):
    """Énumération des étapes du pipeline."""
    PATTERN_ANALYSIS = "pattern_analysis"
    CATEGORIZATION = "categorization"
    TEMPLATE_GENERATION = "template_generation"
    PARAMETER_RESOLUTION = "parameter_resolution"
    EXECUTION = "execution"


class ErrorType(Enum):
    """Types d'erreurs possibles dans le pipeline."""
    PATTERN_ANALYSIS_FAILED = "pattern_analysis_failed"
    CATEGORIZATION_FAILED = "categorization_failed"
    TEMPLATE_GENERATION_FAILED = "template_generation_failed"
    PARAMETER_RESOLUTION_FAILED = "parameter_resolution_failed"
    EXECUTION_FAILED = "execution_failed"
    TIMEOUT_EXCEEDED = "timeout_exceeded"
    INVALID_INPUT = "invalid_input"
    COMPONENT_ERROR = "component_error"


@dataclass
class PuzzleData:
    """
    Structure de données pour un puzzle ARC.
    
    Contient tous les exemples train et le test input.
    """
    puzzle_id: str
    train_examples: List[Tuple[np.ndarray, np.ndarray]]
    test_input: np.ndarray
    expected_output: Optional[np.ndarray] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validation des données après initialisation."""
        if not self.train_examples:
            raise ValueError("Au moins un exemple train est requis")
        
        if self.test_input.size == 0:
            raise ValueError("Test input ne peut pas être vide")
        
        # Vérifier que tous les exemples train ont des formes cohérentes
        for i, (input_grid, output_grid) in enumerate(self.train_examples):
            if input_grid.size == 0 or output_grid.size == 0:
                raise ValueError(f"Exemple train {i} contient des grilles vides")
    
    def get_stats(self) -> Dict[str, Any]:
        """Retourne des statistiques sur le puzzle."""
        input_shapes = [inp.shape for inp, _ in self.train_examples]
        output_shapes = [out.shape for _, out in self.train_examples]
        
        return {
            'puzzle_id': self.puzzle_id,
            'num_train_examples': len(self.train_examples),
            'test_input_shape': self.test_input.shape,
            'train_input_shapes': input_shapes,
            'train_output_shapes': output_shapes,
            'has_expected_output': self.expected_output is not None
        }


@dataclass
class AnalysisResult:
    """
    Résultat de l'analyse de patterns (Étape 1).
    
    Contient les transformations détectées par calculs géométriques.
    """
    success: bool
    individual_analyses: List[Dict[str, Any]] = field(default_factory=list)
    common_patterns: Dict[str, Any] = field(default_factory=dict)
    execution_time_ms: float = 0.0
    method: str = "geometric_calculations"
    error: Optional[str] = None
    
    def get_dominant_pattern(self) -> Optional[str]:
        """Retourne le pattern dominant détecté."""
        if not self.success or not self.common_patterns:
            return None
        
        # Logique pour déterminer le pattern dominant
        patterns = self.common_patterns
        
        if patterns.get('rotation_angles'):
            return "rotation"
        elif patterns.get('symmetry_types'):
            return "symmetry"
        elif patterns.get('color_changes'):
            return "color_change"
        elif patterns.get('size_changes'):
            return "size_change"
        elif patterns.get('copy_paste_detected'):
            return "copy_paste"
        elif patterns.get('fill_pattern_detected'):
            return "fill_pattern"
        else:
            return "unknown"


@dataclass
class CategorizationResult:
    """
    Résultat de la catégorisation (Étape 2).
    
    Contient la catégorie assignée par règles conditionnelles.
    """
    success: bool
    category: str = "unknown"
    confidence: float = 0.0
    method: str = "conditional_rules"
    error: Optional[str] = None
    
    def is_valid_category(self) -> bool:
        """Vérifie si la catégorie est valide."""
        valid_categories = [
            "color_pattern", "geometric_transform", "resize_operation",
            "fill_pattern", "copy_paste", "complex_pattern"
        ]
        return self.category in valid_categories


@dataclass
class TemplateResult:
    """
    Résultat de la génération de template (Étape 3).
    
    Contient le template généré par substitution de variables.
    """
    success: bool
    template: str = ""
    variables: Dict[str, List] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    category: str = ""
    method: str = "template_substitution"
    error: Optional[str] = None
    
    def get_variable_count(self) -> int:
        """Retourne le nombre de variables dans le template."""
        return len(self.variables)
    
    def has_required_variables(self) -> bool:
        """Vérifie si le template a les variables requises."""
        return len(self.variables) > 0 and self.template.count('{') > 0


@dataclass
class SolutionResult:
    """
    Résultat de la résolution de paramètres (Étape 4).
    
    Contient le scénario résolu par recherche algorithmique.
    """
    success: bool
    resolved_scenario: str = ""
    original_template: str = ""
    parameter_values: Dict[str, str] = field(default_factory=dict)
    validation_score: float = 0.0
    combinations_tested: int = 0
    method: str = "brute_force_search"
    error: Optional[str] = None
    
    def is_fully_resolved(self) -> bool:
        """Vérifie si toutes les variables ont été résolues."""
        return self.success and '{' not in self.resolved_scenario and '}' not in self.resolved_scenario


@dataclass
class ExecutionResult:
    """
    Résultat de l'exécution AGI (Étape 5).
    
    Contient la grille de sortie générée par l'interpréteur.
    """
    success: bool
    output_grid: Optional[np.ndarray] = None
    executed_scenario: str = ""
    method: str = "agi_interpreter"
    error: Optional[str] = None
    
    def get_output_shape(self) -> Optional[Tuple[int, int]]:
        """Retourne la forme de la grille de sortie."""
        if self.output_grid is not None:
            return self.output_grid.shape
        return None
    
    def validate_output(self, expected_shape: Optional[Tuple[int, int]] = None) -> bool:
        """Valide la grille de sortie."""
        if not self.success or self.output_grid is None:
            return False
        
        if expected_shape and self.output_grid.shape != expected_shape:
            return False
        
        return True


@dataclass
class PipelineResult:
    """
    Résultat complet du pipeline.
    
    Agrège tous les résultats des étapes.
    """
    success: bool
    puzzle_id: str
    output_grid: Optional[np.ndarray] = None
    execution_time_ms: float = 0.0
    method: str = "algorithmic_pipeline"
    error: Optional[str] = None
    
    # Résultats des étapes
    step1_analysis: Optional[AnalysisResult] = None
    step2_categorization: Optional[CategorizationResult] = None
    step3_template: Optional[TemplateResult] = None
    step4_solution: Optional[SolutionResult] = None
    step5_execution: Optional[ExecutionResult] = None
    
    def get_failed_step(self) -> Optional[PipelineStep]:
        """Retourne la première étape qui a échoué."""
        steps = [
            (PipelineStep.PATTERN_ANALYSIS, self.step1_analysis),
            (PipelineStep.CATEGORIZATION, self.step2_categorization),
            (PipelineStep.TEMPLATE_GENERATION, self.step3_template),
            (PipelineStep.PARAMETER_RESOLUTION, self.step4_solution),
            (PipelineStep.EXECUTION, self.step5_execution)
        ]
        
        for step_type, result in steps:
            if result and not result.success:
                return step_type
        
        return None
    
    def get_success_rate(self) -> float:
        """Retourne le taux de réussite des étapes."""
        steps = [self.step1_analysis, self.step2_categorization, self.step3_template, 
                self.step4_solution, self.step5_execution]
        
        successful_steps = sum(1 for step in steps if step and step.success)
        total_steps = sum(1 for step in steps if step is not None)
        
        return successful_steps / total_steps if total_steps > 0 else 0.0
    
    def to_summary(self) -> Dict[str, Any]:
        """Retourne un résumé du résultat."""
        return {
            'success': self.success,
            'puzzle_id': self.puzzle_id,
            'execution_time_ms': self.execution_time_ms,
            'failed_step': self.get_failed_step().value if self.get_failed_step() else None,
            'success_rate': self.get_success_rate(),
            'output_shape': self.output_grid.shape if self.output_grid is not None else None,
            'error': self.error,
            'method': self.method
        }


# === CONSTANTES D'ERREURS ===

ERROR_MESSAGES = {
    ErrorType.PATTERN_ANALYSIS_FAILED: {
        'message': 'Échec de l\'analyse de patterns par calculs géométriques',
        'description': 'Les algorithmes de détection de rotations, symétries et changements de couleurs ont échoué',
        'method': 'geometric_calculations'
    },
    ErrorType.CATEGORIZATION_FAILED: {
        'message': 'Échec de la catégorisation par règles conditionnelles',
        'description': 'Les règles de classification n\'ont pas pu déterminer une catégorie valide',
        'method': 'conditional_rules'
    },
    ErrorType.TEMPLATE_GENERATION_FAILED: {
        'message': 'Échec de la génération de template par substitution',
        'description': 'La base de données de templates n\'a pas pu générer un template valide',
        'method': 'template_substitution'
    },
    ErrorType.PARAMETER_RESOLUTION_FAILED: {
        'message': 'Échec de la résolution de paramètres par recherche',
        'description': 'La recherche algorithmique n\'a trouvé aucune combinaison de paramètres valide',
        'method': 'brute_force_search'
    },
    ErrorType.EXECUTION_FAILED: {
        'message': 'Échec de l\'exécution par l\'interpréteur AGI',
        'description': 'L\'interpréteur de commandes n\'a pas pu exécuter le scénario généré',
        'method': 'agi_interpreter'
    },
    ErrorType.TIMEOUT_EXCEEDED: {
        'message': 'Timeout dépassé durant l\'exécution',
        'description': 'Le temps d\'exécution a dépassé la limite configurée',
        'method': 'timeout_control'
    },
    ErrorType.INVALID_INPUT: {
        'message': 'Données d\'entrée invalides',
        'description': 'Les données fournies ne respectent pas le format attendu',
        'method': 'input_validation'
    },
    ErrorType.COMPONENT_ERROR: {
        'message': 'Erreur dans un composant du pipeline',
        'description': 'Un composant interne a rencontré une erreur inattendue',
        'method': 'component_execution'
    }
}


def create_error_result(error_type: ErrorType, step: PipelineStep, 
                       additional_info: str = "") -> Dict[str, Any]:
    """
    Crée un résultat d'erreur standardisé.
    
    Args:
        error_type: Type d'erreur
        step: Étape où l'erreur s'est produite
        additional_info: Informations supplémentaires
    
    Returns:
        Dict avec les détails de l'erreur
    """
    error_info = ERROR_MESSAGES[error_type]
    
    return {
        'success': False,
        'error_type': error_type.value,
        'step': step.value,
        'message': error_info['message'],
        'description': error_info['description'],
        'method': error_info['method'],
        'additional_info': additional_info,
        'is_algorithmic': True  # Toujours algorithmique, jamais d'IA
    }


# === STRUCTURES DE CONFIGURATION ===

@dataclass
class PipelineConfig:
    """Configuration du pipeline."""
    timeout_per_puzzle: int = 10
    timeout_parameter_resolution: int = 5
    max_combinations_to_test: int = 1000
    enable_logging: bool = True
    log_level: str = "INFO"
    
    def validate(self) -> bool:
        """Valide la configuration."""
        if self.timeout_per_puzzle <= 0:
            return False
        if self.timeout_parameter_resolution <= 0:
            return False
        if self.max_combinations_to_test <= 0:
            return False
        return True


@dataclass
class ComponentStats:
    """Statistiques d'un composant du pipeline."""
    component_name: str
    method: str
    total_calls: int = 0
    successful_calls: int = 0
    average_execution_time_ms: float = 0.0
    last_error: Optional[str] = None
    
    def get_success_rate(self) -> float:
        """Retourne le taux de réussite du composant."""
        return self.successful_calls / self.total_calls if self.total_calls > 0 else 0.0
    
    def update_stats(self, success: bool, execution_time_ms: float, error: Optional[str] = None):
        """Met à jour les statistiques."""
        self.total_calls += 1
        if success:
            self.successful_calls += 1
        
        # Moyenne mobile simple
        self.average_execution_time_ms = (
            (self.average_execution_time_ms * (self.total_calls - 1) + execution_time_ms) 
            / self.total_calls
        )
        
        if error:
            self.last_error = error


@dataclass
class PipelineStats:
    """Statistiques globales du pipeline."""
    total_puzzles_processed: int = 0
    successful_puzzles: int = 0
    average_execution_time_ms: float = 0.0
    component_stats: Dict[str, ComponentStats] = field(default_factory=dict)
    
    def get_success_rate(self) -> float:
        """Retourne le taux de réussite global."""
        return self.successful_puzzles / self.total_puzzles_processed if self.total_puzzles_processed > 0 else 0.0
    
    def add_component_stats(self, component_name: str, method: str) -> ComponentStats:
        """Ajoute les statistiques d'un composant."""
        stats = ComponentStats(component_name=component_name, method=method)
        self.component_stats[component_name] = stats
        return stats
    
    def update_global_stats(self, success: bool, execution_time_ms: float):
        """Met à jour les statistiques globales."""
        self.total_puzzles_processed += 1
        if success:
            self.successful_puzzles += 1
        
        # Moyenne mobile simple
        self.average_execution_time_ms = (
            (self.average_execution_time_ms * (self.total_puzzles_processed - 1) + execution_time_ms) 
            / self.total_puzzles_processed
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertit les statistiques en dictionnaire."""
        return {
            'global': {
                'total_puzzles_processed': self.total_puzzles_processed,
                'successful_puzzles': self.successful_puzzles,
                'success_rate': self.get_success_rate(),
                'average_execution_time_ms': self.average_execution_time_ms
            },
            'components': {
                name: {
                    'method': stats.method,
                    'total_calls': stats.total_calls,
                    'successful_calls': stats.successful_calls,
                    'success_rate': stats.get_success_rate(),
                    'average_execution_time_ms': stats.average_execution_time_ms,
                    'last_error': stats.last_error
                }
                for name, stats in self.component_stats.items()
            }
        }
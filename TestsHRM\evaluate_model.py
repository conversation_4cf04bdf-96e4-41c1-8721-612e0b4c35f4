#!/usr/bin/env python3
"""
Comprehensive evaluation script for the trained HRM model on ARC dataset
"""

import torch
import json
import os
import time
import numpy as np
from datetime import datetime
from pathlib import Path
import argparse
from tqdm import tqdm
import traceback

# Import our modules
from config import Config
from src.tokenizer import GrammarTokenizer
from models.hrm_model import GridToProgramHRM
from models.inference import generate_program, load_arc_puzzle, evaluate_single_puzzle, execute_agi_program


class ARCEvaluator:
    """Comprehensive evaluator for HRM model on ARC dataset"""
    
    def __init__(self, model_path="hrm_arc_solver.pth", config=None):
        self.config = config or Config()
        self.device = self.config.DEVICE
        
        # Initialize tokenizer
        self.tokenizer = GrammarTokenizer()
        
        # Load model
        self.model = self._load_model(model_path)
        
        # Results storage
        self.results = {
            'total_puzzles': 0,
            'solved_puzzles': 0,
            'total_test_cases': 0,
            'solved_test_cases': 0,
            'average_inference_time': 0,
            'puzzle_results': [],
            'evaluation_timestamp': datetime.now().isoformat(),
            'model_path': model_path
        }
    
    def _load_model(self, model_path):
        """Load the trained HRM model"""
        print(f"Loading model from {model_path}...")
        
        # Initialize model
        model = GridToProgramHRM(
            model_dim=self.config.MODEL_DIM,
            n_heads=self.config.N_HEADS,
            grammar_vocab_size=len(self.tokenizer.vocab),
            N_cycles=self.config.N_CYCLES,
            T_steps=self.config.T_STEPS
        )
        
        # Load weights
        if os.path.exists(model_path):
            state_dict = torch.load(model_path, map_location=self.device)
            model.load_state_dict(state_dict)
            print("Model loaded successfully!")
        else:
            print(f"Warning: Model file {model_path} not found. Using untrained model.")
        
        model.to(self.device)
        model.eval()
        return model
    
    def evaluate_dataset(self, dataset_path="arcdata/evaluation", max_puzzles=None, save_programs=True):
        """Evaluate model on entire ARC evaluation dataset"""
        print(f"Evaluating on dataset: {dataset_path}")
        
        # Get all JSON files
        json_files = [f for f in os.listdir(dataset_path) if f.endswith('.json')]
        
        if max_puzzles:
            json_files = json_files[:max_puzzles]
        
        print(f"Found {len(json_files)} puzzles to evaluate")
        
        total_inference_time = 0
        
        for json_file in tqdm(json_files, desc="Evaluating puzzles"):
            puzzle_id = json_file.replace('.json', '')
            json_path = os.path.join(dataset_path, json_file)
            
            try:
                # Load puzzle
                puzzle_data = load_arc_puzzle(json_path)
                
                # Evaluate puzzle
                puzzle_results = evaluate_single_puzzle(
                    self.model, self.tokenizer, puzzle_data, self.device
                )
                
                # Process results
                puzzle_solved = all(result['is_correct'] for result in puzzle_results)
                test_cases_solved = sum(1 for result in puzzle_results if result['is_correct'])
                avg_time = np.mean([result['inference_time'] for result in puzzle_results])
                
                # Update statistics
                self.results['total_puzzles'] += 1
                self.results['total_test_cases'] += len(puzzle_results)
                self.results['solved_test_cases'] += test_cases_solved
                total_inference_time += avg_time
                
                if puzzle_solved:
                    self.results['solved_puzzles'] += 1
                
                # Store detailed results
                puzzle_result = {
                    'puzzle_id': puzzle_id,
                    'puzzle_solved': puzzle_solved,
                    'test_cases_total': len(puzzle_results),
                    'test_cases_solved': test_cases_solved,
                    'average_inference_time': avg_time,
                    'test_cases': puzzle_results
                }
                
                self.results['puzzle_results'].append(puzzle_result)
                
                # Save generated programs if requested
                if save_programs:
                    self._save_generated_programs(dataset_path, puzzle_id, puzzle_results)
                
            except Exception as e:
                print(f"Error evaluating puzzle {puzzle_id}: {e}")
                traceback.print_exc()
                continue
        
        # Calculate final statistics
        if self.results['total_puzzles'] > 0:
            self.results['average_inference_time'] = total_inference_time / self.results['total_puzzles']
        
        return self.results
    
    def _save_generated_programs(self, dataset_path, puzzle_id, puzzle_results):
        """Save generated AGI programs to files"""
        for i, result in enumerate(puzzle_results):
            if result['generated_program'].strip():
                # Create filename
                timestamp = datetime.now().strftime("%Y-%m-%dT%H-%M-%S-%fZ")[:-3] + "Z"
                status = "VALID" if result['is_correct'] else "INVALID"
                filename = f"{puzzle_id}_TEST{i}_{status}_{timestamp}.agi"
                filepath = os.path.join(dataset_path, filename)
                
                # Save program
                try:
                    with open(filepath, 'w') as f:
                        f.write(result['generated_program'])
                    
                    # Also save metadata
                    metadata = {
                        'puzzle_id': puzzle_id,
                        'test_case_index': i,
                        'is_correct': result['is_correct'],
                        'inference_time': result['inference_time'],
                        'timestamp': timestamp,
                        'model_config': {
                            'model_dim': self.config.MODEL_DIM,
                            'n_heads': self.config.N_HEADS,
                            'n_cycles': self.config.N_CYCLES,
                            't_steps': self.config.T_STEPS
                        }
                    }
                    
                    metadata_path = os.path.join(dataset_path, "metadata", f"{filename}.metadata")
                    os.makedirs(os.path.dirname(metadata_path), exist_ok=True)
                    
                    with open(metadata_path, 'w') as f:
                        json.dump(metadata, f, indent=2)
                        
                except Exception as e:
                    print(f"Error saving program for {puzzle_id}: {e}")
    
    def print_summary(self):
        """Print evaluation summary"""
        results = self.results
        
        print("\n" + "="*60)
        print("HRM MODEL EVALUATION SUMMARY")
        print("="*60)
        print(f"Total puzzles evaluated: {results['total_puzzles']}")
        print(f"Puzzles solved: {results['solved_puzzles']}")
        print(f"Puzzle accuracy: {results['solved_puzzles']/max(results['total_puzzles'], 1)*100:.2f}%")
        print()
        print(f"Total test cases: {results['total_test_cases']}")
        print(f"Test cases solved: {results['solved_test_cases']}")
        print(f"Test case accuracy: {results['solved_test_cases']/max(results['total_test_cases'], 1)*100:.2f}%")
        print()
        print(f"Average inference time: {results['average_inference_time']:.3f}s")
        print(f"Evaluation completed: {results['evaluation_timestamp']}")
        print("="*60)
    
    def save_results(self, output_path="evaluation_results.json"):
        """Save detailed results to JSON file"""
        with open(output_path, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        print(f"Detailed results saved to: {output_path}")


def main():
    parser = argparse.ArgumentParser(description="Evaluate HRM model on ARC dataset")
    parser.add_argument("--model", default="hrm_arc_solver.pth", help="Path to trained model")
    parser.add_argument("--dataset", default="arcdata/evaluation", help="Path to evaluation dataset")
    parser.add_argument("--max-puzzles", type=int, help="Maximum number of puzzles to evaluate")
    parser.add_argument("--output", default="evaluation_results.json", help="Output file for results")
    parser.add_argument("--no-save-programs", action="store_true", help="Don't save generated programs")
    
    args = parser.parse_args()
    
    # Initialize evaluator
    evaluator = ARCEvaluator(model_path=args.model)
    
    # Run evaluation
    print("Starting HRM model evaluation...")
    results = evaluator.evaluate_dataset(
        dataset_path=args.dataset,
        max_puzzles=args.max_puzzles,
        save_programs=not args.no_save_programs
    )
    
    # Print summary
    evaluator.print_summary()
    
    # Save results
    evaluator.save_results(args.output)


if __name__ == "__main__":
    main()

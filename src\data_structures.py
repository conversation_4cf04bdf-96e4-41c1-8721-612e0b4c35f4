"""
Modèles de données et structures pour le pipeline ARC-Solver.

Ce module définit les structures de données utilisées par le pipeline
de résolution ARC basé sur des algorithmes explicites et des calculs programmés.
Aucune prétention à des capacités d'IA inexistantes.
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Any
import numpy as np
import time


@dataclass
class PuzzleData:
    """
    Structure de données pour un puzzle ARC complet.
    
    Contient tous les exemples d'entraînement et l'input de test.
    Chargement par parsing JSON classique, pas d'IA.
    """
    puzzle_id: str
    train_inputs: List[np.ndarray]
    train_outputs: List[np.ndarray]
    test_input: np.ndarray
    expected_output: Optional[np.ndarray] = None
    
    def __post_init__(self):
        """Validation des données après initialisation."""
        if len(self.train_inputs) != len(self.train_outputs):
            raise ValueError(f"Nombre d'inputs ({len(self.train_inputs)}) != "
                           f"nombre d'outputs ({len(self.train_outputs)})")
        
        if len(self.train_inputs) == 0:
            raise ValueError("Aucun exemple d'entraînement fourni")


@dataclass
class AnalysisResult:
    """
    Résultat de l'analyse de patterns par calculs algorithmiques explicites.
    
    Chaque 'détection' provient d'algorithmes programmés :
    - Rotations : comparaison matricielle
    - Symétries : calculs d'axes géométriques
    - Couleurs : histogrammes et statistiques
    """
    category: str
    confidence: float
    detected_transformations: List[str]
    analysis_details: Dict[str, Any]
    execution_time_ms: float
    
    def __post_init__(self):
        """Validation des résultats d'analyse."""
        valid_categories = [
            "geometric_transform", "color_pattern", "resize_operation",
            "copy_paste", "fill_pattern", "unknown"
        ]
        if self.category not in valid_categories:
            raise ValueError(f"Catégorie invalide: {self.category}")
        
        if not 0.0 <= self.confidence <= 1.0:
            raise ValueError(f"Confidence doit être entre 0.0 et 1.0: {self.confidence}")


@dataclass
class TemplateResult:
    """
    Résultat de la génération de template par substitution de variables.
    
    Génération par base de données de patterns pré-définis, pas d'IA.
    Chaque template est sélectionné selon des règles conditionnelles explicites.
    """
    template: str
    variables: Dict[str, List]
    constraints: Dict[str, Any]
    generation_method: str  # "rule_based", pas "learned"
    category_used: str
    
    def __post_init__(self):
        """Validation du template généré."""
        if self.generation_method != "rule_based":
            raise ValueError("Seule la méthode 'rule_based' est supportée")
        
        if not self.template:
            raise ValueError("Template ne peut pas être vide")


@dataclass
class SolutionResult:
    """
    Résultat de la résolution par recherche algorithmique.
    
    Résolution par force brute optimisée, pas d'apprentissage automatique.
    Chaque solution est trouvée par énumération et test de combinaisons.
    """
    success: bool
    solution_command: Optional[str]
    execution_time_ms: float
    validation_score: float
    error_message: Optional[str]
    method_used: str  # "algorithmic_search", pas "ai_inference"
    parameters_tested: int
    timeout_reached: bool
    
    def __post_init__(self):
        """Validation du résultat de résolution."""
        if self.method_used != "algorithmic_search":
            raise ValueError("Seule la méthode 'algorithmic_search' est supportée")
        
        if not 0.0 <= self.validation_score <= 1.0:
            raise ValueError(f"Score de validation invalide: {self.validation_score}")
        
        if self.success and not self.solution_command:
            raise ValueError("Solution réussie mais commande manquante")


@dataclass
class PipelineResult:
    """
    Résultat complet du pipeline de résolution ARC.
    
    Orchestration transparente des composants algorithmiques.
    Chaque étape est tracée avec sa méthode et son temps d'exécution.
    """
    puzzle_id: str
    success: bool
    final_solution: Optional[str]
    analysis_result: Optional[AnalysisResult]
    template_result: Optional[TemplateResult]
    solution_result: Optional[SolutionResult]
    total_execution_time_ms: float
    error_step: Optional[str]
    error_message: Optional[str]
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """
        Résumé transparent de l'exécution.
        
        Indique clairement les méthodes utilisées (calculs programmés)
        et les performances algorithmiques.
        """
        return {
            "puzzle_id": self.puzzle_id,
            "success": self.success,
            "total_time_ms": self.total_execution_time_ms,
            "analysis_method": "geometric_calculations" if self.analysis_result else None,
            "template_method": "rule_based_substitution" if self.template_result else None,
            "resolution_method": "algorithmic_search" if self.solution_result else None,
            "error_step": self.error_step,
            "transparency_note": "Tous les résultats proviennent d'algorithmes programmés, pas d'IA"
        }


# Messages d'erreur honnêtes et explicites
ERROR_MESSAGES = {
    "no_pattern_detected": "Aucun pattern géométrique détecté par les algorithmes programmés",
    "template_not_found": "Aucun template pré-défini pour cette catégorie",
    "parameter_search_failed": "La recherche algorithmique n'a trouvé aucune solution valide",
    "timeout_exceeded": "Timeout dépassé lors de la recherche par force brute",
    "execution_failed": "Échec de l'exécution de la commande AGI générée",
    "json_corrupted": "Fichier JSON corrompu ou format invalide",
    "grid_size_mismatch": "Tailles de grilles incompatibles pour la normalisation",
    "invalid_command": "Commande AGI générée invalide selon la grammaire",
    "validation_failed": "Échec de la validation sur les exemples d'entraînement"
}


# Configuration des timeouts et paramètres
class PipelineConfig:
    """
    Configuration du pipeline avec limites explicites.
    
    Tous les timeouts sont définis pour éviter les boucles infinies
    dans les algorithmes de recherche par force brute.
    """
    
    # Timeouts en secondes
    PATTERN_ANALYSIS_TIMEOUT = 2
    PARAMETER_RESOLUTION_TIMEOUT = 10
    TOTAL_PIPELINE_TIMEOUT = 15
    
    # Limites de recherche
    MAX_PARAMETER_COMBINATIONS = 10000
    MAX_GRID_SIZE = 30
    
    # Seuils de qualité
    MIN_VALIDATION_SCORE = 0.8
    MIN_CONFIDENCE_THRESHOLD = 0.5
    
    # Métriques de performance cibles
    TARGET_RESOLUTION_RATE = 0.30  # 30% de puzzles résolus
    MAX_EXECUTION_TIME_MS = 10000   # 10 secondes par puzzle


class ExecutionTimer:
    """
    Utilitaire pour mesurer les temps d'exécution algorithmique.
    
    Permet de tracer précisément les performances de chaque composant
    sans prétendre à des optimisations automatiques.
    """
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """Démarrer le chronométrage."""
        self.start_time = time.time()
    
    def stop(self) -> float:
        """
        Arrêter le chronométrage et retourner le temps en millisecondes.
        
        Returns:
            Temps d'exécution en millisecondes
        """
        if self.start_time is None:
            raise ValueError("Timer non démarré")
        
        self.end_time = time.time()
        return (self.end_time - self.start_time) * 1000
    
    def get_elapsed_ms(self) -> float:
        """
        Obtenir le temps écoulé sans arrêter le timer.
        
        Returns:
            Temps écoulé en millisecondes
        """
        if self.start_time is None:
            raise ValueError("Timer non démarré")
        
        current_time = time.time()
        return (current_time - self.start_time) * 1000


def create_error_result(error_type: str, puzzle_id: str, 
                       execution_time_ms: float = 0.0) -> PipelineResult:
    """
    Créer un résultat d'erreur avec message explicite.
    
    Args:
        error_type: Type d'erreur (clé dans ERROR_MESSAGES)
        puzzle_id: Identifiant du puzzle
        execution_time_ms: Temps d'exécution avant l'erreur
    
    Returns:
        PipelineResult avec erreur documentée
    """
    error_message = ERROR_MESSAGES.get(error_type, f"Erreur inconnue: {error_type}")
    
    return PipelineResult(
        puzzle_id=puzzle_id,
        success=False,
        final_solution=None,
        analysis_result=None,
        template_result=None,
        solution_result=None,
        total_execution_time_ms=execution_time_ms,
        error_step=error_type,
        error_message=error_message
    )


def validate_puzzle_data(puzzle_data: PuzzleData) -> bool:
    """
    Validation des données de puzzle par vérifications algorithmiques.
    
    Effectue des contrôles de cohérence sur les données :
    - Formats des grilles
    - Cohérence des tailles
    - Validité des valeurs
    
    Args:
        puzzle_data: Données du puzzle à valider
    
    Returns:
        True si les données sont valides
    
    Raises:
        ValueError: Si les données sont invalides
    """
    # Vérifier que toutes les grilles sont des numpy arrays
    for i, grid in enumerate(puzzle_data.train_inputs):
        if not isinstance(grid, np.ndarray):
            raise ValueError(f"Train input {i} n'est pas un numpy array")
    
    for i, grid in enumerate(puzzle_data.train_outputs):
        if not isinstance(grid, np.ndarray):
            raise ValueError(f"Train output {i} n'est pas un numpy array")
    
    if not isinstance(puzzle_data.test_input, np.ndarray):
        raise ValueError("Test input n'est pas un numpy array")
    
    # Vérifier les valeurs des grilles (doivent être des entiers 0-9)
    all_grids = (puzzle_data.train_inputs + puzzle_data.train_outputs + 
                [puzzle_data.test_input])
    
    for i, grid in enumerate(all_grids):
        if grid.dtype != np.int32 and grid.dtype != np.int64:
            raise ValueError(f"Grille {i} n'a pas le bon type (attendu: int)")
        
        if np.any((grid < 0) | (grid > 9)):
            raise ValueError(f"Grille {i} contient des valeurs invalides (hors 0-9)")
    
    return True
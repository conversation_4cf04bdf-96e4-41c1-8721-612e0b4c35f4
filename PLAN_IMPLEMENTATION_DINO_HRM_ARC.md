# Plan d'Implémentation : Architecture DINO-HRM pour ARC

## 🎯 Vision Globale

Créer un système à deux étages reproduisant le processus cognitif humain pour résoudre les puzzles ARC :

1. **DINO** : Analyse comparative des exemples train → Catégorisation automatique → Génération de scénario généralisé
2. **HRM** : Application du scénario → Ajustement des paramètres → Validation → Correction itérative

## 🏗️ Architecture Générale

```
Input: puzzle.json (train examples)
    ↓
┌─────────────────────────────────────┐
│           DINO ANALYZER             │
│  ┌─────────────────────────────────┐│
│  │ Multi-Example Comparison        ││
│  │ - train[0]: input → output      ││
│  │ - train[1]: input → output      ││
│  │ - train[2]: input → output      ││
│  └─────────────────────────────────┘│
│              ↓                      │
│  ┌─────────────────────────────────┐│
│  │ Pattern Detection & Clustering  ││
│  │ - Geometric patterns            ││
│  │ - Color transformations         ││
│  │ - Spatial relationships         ││
│  └─────────────────────────────────┘│
│              ↓                      │
│  ┌─────────────────────────────────┐│
│  │ Auto-Categorization             ││
│  │ - "mosaic_complex"              ││
│  │ - "geometric_transform"         ││
│  │ - "color_pattern"               ││
│  └─────────────────────────────────┘│
│              ↓                      │
│  ┌─────────────────────────────────┐│
│  │ Generalized Scenario Generator  ││
│  │ - Abstract command sequence     ││
│  │ - Parameter placeholders        ││
│  │ - Validation constraints        ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
    ↓
Output: Generalized Scenario
    "RESIZE {factor}x{factor}; MOTIF {pattern_type} {color_var}"
    ↓
┌─────────────────────────────────────┐
│            HRM EXECUTOR             │
│  ┌─────────────────────────────────┐│
│  │ Parameter Instantiation         ││
│  │ - factor = 3                    ││
│  │ - pattern_type = COPY           ││
│  │ - color_var = 7                 ││
│  └─────────────────────────────────┘│
│              ↓                      │
│  ┌─────────────────────────────────┐│
│  │ Command Execution               ││
│  │ - Apply on test_input           ││
│  │ - Generate test_output          ││
│  └─────────────────────────────────┘│
│              ↓                      │
│  ┌─────────────────────────────────┐│
│  │ Validation Loop                 ││
│  │ - Check against train examples  ││
│  │ - Adjust parameters if needed   ││
│  │ - Retry or escalate to DINO     ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
    ↓
Output: test_output_predicted
```

## 📋 Composants à Développer

### 1. **DINO-ARC Adapter**
**Fichier :** `models/dino_arc.py`

**Responsabilités :**
- Charger les puzzles JSON (train examples multiples)
- Encoder les grilles avec embeddings valeur + position
- Détecter les patterns par comparaison multi-exemples
- Catégoriser automatiquement les puzzles
- Générer des scénarios généralisés

**Inputs :**
```python
puzzle_data = {
    "train": [
        {"input": [[0,1,2], [1,0,1]], "output": [[0,0,0], [0,0,0]]},
        {"input": [[1,2,0], [2,1,2]], "output": [[1,1,1], [1,1,1]]},
        # ...
    ]
}
```

**Outputs :**
```python
analysis_result = {
    "category": "color_fill_pattern",
    "confidence": 0.87,
    "generalized_scenario": "FILL {dominant_color} [0,0 {max_x},{max_y}]",
    "parameters": {
        "dominant_color": "variable",
        "max_x": "grid_width-1", 
        "max_y": "grid_height-1"
    }
}
```

### 2. **Scenario Generalizer**
**Fichier :** `src/scenario_generalizer.py`

**Responsabilités :**
- Analyser les commandes AGI existantes
- Identifier les paramètres variables (couleurs, coordonnées)
- Créer des templates généralisés
- Mapper catégories → templates

**Exemple de généralisation :**
```python
# Original AGI
"EDIT 7 ([0,0] [1,1] [2,2]); EDIT 3 ([0,1] [1,2] [2,0])"

# Généralisé
"EDIT {color_A} {pattern_A}; EDIT {color_B} {pattern_B}"

# Avec contraintes
{
    "color_A": "most_frequent_color",
    "color_B": "second_most_frequent_color", 
    "pattern_A": "diagonal_main",
    "pattern_B": "diagonal_anti"
}
```

### 3. **HRM Parameter Resolver**
**Fichier :** `models/hrm_parameter_resolver.py`

**Responsabilités :**
- Recevoir le scénario généralisé de DINO
- Instancier les paramètres pour le test_input spécifique
- Exécuter les commandes avec les paramètres résolus
- Valider le résultat sur les exemples train
- Ajuster les paramètres en cas d'échec

**Pipeline :**
```python
def resolve_and_execute(generalized_scenario, test_input, train_examples):
    # 1. Résolution des paramètres
    parameters = resolve_parameters(generalized_scenario, test_input)
    
    # 2. Instanciation du scénario
    concrete_scenario = instantiate_scenario(generalized_scenario, parameters)
    
    # 3. Exécution
    predicted_output = execute_scenario(concrete_scenario, test_input)
    
    # 4. Validation
    validation_score = validate_on_train(concrete_scenario, train_examples)
    
    # 5. Ajustement si nécessaire
    if validation_score < threshold:
        parameters = adjust_parameters(parameters, validation_errors)
        return resolve_and_execute(generalized_scenario, test_input, train_examples)
    
    return predicted_output
```

### 4. **Multi-Example Dataset**
**Fichier :** `src/multi_example_dataset.py`

**Responsabilités :**
- Charger tous les exemples train d'un puzzle (pas seulement le premier)
- Fournir les données à DINO pour l'analyse comparative
- Gérer le padding et la normalisation des grilles de tailles différentes

**Structure :**
```python
class MultiExampleARCDataset(Dataset):
    def __getitem__(self, idx):
        puzzle = self.puzzles[idx]
        
        # Tous les exemples train
        train_inputs = [ex["input"] for ex in puzzle["train"]]
        train_outputs = [ex["output"] for ex in puzzle["train"]]
        
        # Test input (pour HRM)
        test_input = puzzle["test"][0]["input"]
        
        return {
            "train_pairs": list(zip(train_inputs, train_outputs)),
            "test_input": test_input,
            "puzzle_id": puzzle["id"]
        }
```

### 5. **Category-Scenario Mapping**
**Fichier :** `config/category_scenarios.json`

**Responsabilités :**
- Mapper les catégories détectées par DINO aux templates de scénarios
- Définir les contraintes de paramètres par catégorie
- Permettre l'évolution et l'apprentissage de nouveaux mappings

**Structure :**
```json
{
    "mosaic_complex": {
        "templates": [
            "RESIZE {scale}x{scale}; MOTIF {operation} {pattern}",
            "COPY {region}; PASTE {positions}"
        ],
        "constraints": {
            "scale": {"type": "int", "range": [2, 5]},
            "operation": {"type": "enum", "values": ["COPY", "MULTIPLY"]},
            "pattern": {"type": "region", "detection": "auto"}
        }
    },
    "geometric_transform": {
        "templates": [
            "ROTATE {angle} {center}",
            "FLIP {axis} {region}"
        ],
        "constraints": {
            "angle": {"type": "enum", "values": [90, 180, 270]},
            "axis": {"type": "enum", "values": ["HORIZONTAL", "VERTICAL"]}
        }
    }
}
```

## 🔄 Workflow d'Entraînement

### Phase 1 : Pré-entraînement DINO
1. **Auto-supervision** sur grilles masquées
2. **Apprentissage contrastif** avec augmentations (rotations, permutations couleurs)
3. **Clustering** automatique des embeddings pour découvrir les catégories

### Phase 2 : Entraînement DINO supervisé
1. **Fine-tuning** sur les puzzles ARC avec catégories connues
2. **Optimisation** de la génération de scénarios généralisés
3. **Validation** sur la capacité à généraliser

### Phase 3 : Entraînement HRM
1. **Entraînement** sur scénarios généralisés → paramètres concrets
2. **Apprentissage** de la résolution de paramètres
3. **Optimisation** de la boucle de validation/correction

### Phase 4 : Entraînement conjoint
1. **Fine-tuning** end-to-end DINO + HRM
2. **Optimisation** de la communication entre les deux modèles
3. **Validation** sur puzzles complets

## 📊 Métriques d'Évaluation

### DINO
- **Précision de catégorisation** : % de puzzles correctement catégorisés
- **Qualité des scénarios** : % de scénarios généralisés valides
- **Cohérence multi-exemples** : Stabilité des prédictions sur différents train

### HRM  
- **Précision de résolution** : % de paramètres correctement résolus
- **Taux de validation** : % de solutions validées sur train examples
- **Efficacité de correction** : Nombre moyen d'itérations pour converger

### Système complet
- **Accuracy ARC** : % de puzzles résolus correctement
- **Temps de résolution** : Temps moyen par puzzle
- **Robustesse** : Performance sur puzzles non vus

## 🛠️ Technologies et Dépendances

### Frameworks
- **PyTorch** : Modèles DINO et HRM
- **Transformers** : Architecture attention pour DINO
- **Scikit-learn** : Clustering pour catégorisation

### Outils spécialisés
- **Lark** : Parsing de la grammaire AGI
- **NumPy** : Manipulation des grilles
- **Matplotlib** : Visualisation des patterns

## 📅 Planning de Développement

### Sprint 1 (2 semaines) : Fondations
- [ ] Multi-Example Dataset
- [ ] DINO-ARC Adapter (version basique)
- [ ] Scenario Generalizer (templates manuels)

### Sprint 2 (2 semaines) : DINO
- [ ] Auto-catégorisation
- [ ] Génération de scénarios généralisés
- [ ] Validation sur puzzles simples

### Sprint 3 (2 semaines) : HRM
- [ ] Parameter Resolver
- [ ] Boucle de validation/correction
- [ ] Intégration avec DINO

### Sprint 4 (2 semaines) : Intégration
- [ ] Pipeline complet DINO → HRM
- [ ] Entraînement end-to-end
- [ ] Évaluation sur dataset ARC complet

## 🎯 Objectifs de Performance

### Court terme (MVP)
- **30% accuracy** sur puzzles ARC simples
- **5 catégories** automatiquement détectées
- **Pipeline fonctionnel** DINO → HRM

### Moyen terme
- **50% accuracy** sur dataset ARC complet
- **10+ catégories** avec scénarios spécialisés
- **Temps < 30s** par puzzle

### Long terme
- **70%+ accuracy** (niveau compétitif)
- **Généralisation** à nouveaux types de puzzles
- **Explicabilité** des décisions prises

---

Ce plan constitue la roadmap complète pour implémenter l'architecture DINO-HRM adaptée aux puzzles ARC, en respectant les principes d'honnêteté technique et en se concentrant sur des algorithmes explicites plutôt que sur des "capacités magiques".
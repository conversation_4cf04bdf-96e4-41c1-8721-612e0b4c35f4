"""
HRMParameterResolver pour la résolution algorithmique.

Résolution de paramètres par recherche algorithmique.
Force brute optimisée, pas d'apprentissage automatique.
"""

import numpy as np
import time
import itertools
from typing import Dict, List, Optional, Tuple, Any
from src.command_executor import CommandExecutor


class HRMParameterResolver:
    """
    Résolution de paramètres par recherche algorithmique.
    Force brute optimisée, pas d'apprentissage automatique.
    """
    
    def __init__(self, timeout_seconds: int = 10):
        """Initialise le résolveur avec timeout et exécuteur de commandes."""
        self.timeout_seconds = timeout_seconds
        self.command_executor = CommandExecutor()
        
    def resolve_parameters(self, template: str, test_input: np.ndarray, 
                          train_examples: List) -> Optional[str]:
        """
        Algorithme de résolution :
        1. Énumérer valeurs possibles pour chaque variable
        2. Tester combinaisons sur train examples
        3. Sélectionner meilleure combinaison (score)
        4. Appliquer au test_input
        """
        start_time = time.time()
        
        # 1. Énumérer valeurs possibles pour chaque variable
        parameter_values = self._enumerate_parameter_values(template, train_examples)
        
        if not parameter_values:
            return None
        
        # 2. Tester combinaisons sur train examples
        best_scenario = None
        best_score = 0.0
        
        # Générer toutes les combinaisons possibles (force brute optimisée)
        param_names = list(parameter_values.keys())
        param_combinations = itertools.product(*[parameter_values[name] for name in param_names])
        
        combinations_tested = 0
        for combination in param_combinations:
            # Vérifier le timeout
            if time.time() - start_time > self.timeout_seconds:
                print(f"Timeout dépassé ({self.timeout_seconds}s) après {combinations_tested} combinaisons")
                break
            
            # Créer le scénario avec cette combinaison
            scenario = template
            for i, param_name in enumerate(param_names):
                scenario = scenario.replace(f"{{{param_name}}}", str(combination[i]))
            
            # 3. Tester sur les exemples train
            score = self.validate_solution(scenario, train_examples)
            combinations_tested += 1
            
            if score > best_score:
                best_score = score
                best_scenario = scenario
                
                # Si score parfait, arrêter la recherche (early stopping)
                if score >= 1.0:
                    print(f"Solution parfaite trouvée après {combinations_tested} combinaisons")
                    break
        
        print(f"Recherche terminée: {combinations_tested} combinaisons testées, meilleur score: {best_score:.2f}")
        
        # Pour la démonstration, retourner le meilleur scénario même avec score faible
        # Dans un vrai système, on utiliserait un seuil plus strict
        if best_scenario is not None:
            return best_scenario
        
        # Fallback: retourner le template avec des valeurs par défaut
        if parameter_values:
            fallback_scenario = template
            param_names = list(parameter_values.keys())
            for param_name in param_names:
                if parameter_values[param_name]:
                    fallback_scenario = fallback_scenario.replace(f"{{{param_name}}}", str(parameter_values[param_name][0]))
            print(f"Utilisation du scénario de fallback: {fallback_scenario}")
            return fallback_scenario
        
        return None
    
    def validate_solution(self, scenario: str, train_examples: List) -> float:
        """
        Validation par exécution :
        - Exécuter scenario sur chaque train input
        - Comparer avec train output attendu
        - Calculer score de correspondance (0.0 à 1.0)
        """
        if not train_examples:
            return 0.0
        
        successful_executions = 0
        total_examples = len(train_examples)
        
        for input_grid, expected_output in train_examples:
            try:
                # Exécuter le scénario sur cet exemple
                result = self._execute_scenario_on_grid(scenario, input_grid)
                
                if result is not None and np.array_equal(result, expected_output):
                    successful_executions += 1
                    
            except Exception:
                continue  # Échec d'exécution pour cet exemple
        
        return successful_executions / total_examples
    
    def _enumerate_parameter_values(self, template: str, train_examples: List) -> Dict[str, List]:
        """Énumération des valeurs possibles pour chaque paramètre"""
        import re
        
        # Extraire les variables du template
        variables = re.findall(r'\{([^}]+)\}', template)
        parameter_values = {}
        
        for var in variables:
            if 'color' in var.lower():
                parameter_values[var] = self._get_possible_colors(train_examples)
            elif 'angle' in var.lower():
                parameter_values[var] = [90, 180, 270]
            elif 'center' in var.lower():
                parameter_values[var] = self._get_possible_centers(train_examples)
            elif 'size' in var.lower() or 'width' in var.lower() or 'height' in var.lower():
                parameter_values[var] = self._get_possible_sizes(train_examples)
            elif 'region' in var.lower() or 'area' in var.lower() or 'positions' in var.lower():
                parameter_values[var] = self._get_possible_regions(train_examples)
            elif 'axis' in var.lower():
                parameter_values[var] = ['horizontal', 'vertical']
            elif 'factor' in var.lower():
                parameter_values[var] = [2, 3, 4]
            elif 'method' in var.lower():
                parameter_values[var] = ['nearest']
            else:
                parameter_values[var] = ['1', '2', '3']  # Valeurs par défaut
        
        return parameter_values
    
    def _test_parameter_combination(self, scenario: str, train_examples: List) -> bool:
        """Test d'une combinaison de paramètres sur tous les exemples"""
        return self.validate_solution(scenario, train_examples) >= 0.5
    
    def _get_possible_colors(self, train_examples: List) -> List[str]:
        """Extrait les couleurs possibles des exemples"""
        colors = set()
        for input_grid, output_grid in train_examples:
            colors.update(np.unique(input_grid))
            colors.update(np.unique(output_grid))
        # Exclure le fond (0) et limiter à 5 couleurs pour éviter l'explosion combinatoire
        return [str(c) for c in sorted(colors) if c != 0][:5]
    
    def _get_possible_centers(self, train_examples: List) -> List[str]:
        """Extrait les centres possibles"""
        centers = []
        for input_grid, _ in train_examples:
            h, w = input_grid.shape
            centers.append(f"[{h//2},{w//2}]")
        return list(set(centers))
    
    def _get_possible_sizes(self, train_examples: List) -> List[str]:
        """Extrait les tailles possibles"""
        sizes = []
        for input_grid, output_grid in train_examples:
            h1, w1 = input_grid.shape
            h2, w2 = output_grid.shape
            sizes.extend([str(w1), str(h1), str(w2), str(h2), f"{w1}x{h1}", f"{w2}x{h2}"])
        # Limiter pour éviter l'explosion combinatoire
        return list(set(sizes))[:5]
    
    def _get_possible_regions(self, train_examples: List) -> List[str]:
        """Extrait les régions possibles"""
        regions = []
        for input_grid, _ in train_examples:
            h, w = input_grid.shape
            regions.extend([
                f"[0,0 {w},{h}]",  # Grille complète
                f"[0,0 {w//2},{h//2}]",  # Quart supérieur gauche
                f"[{w//2},{h//2} {w},{h}]"  # Quart inférieur droit
            ])
        # Limiter pour éviter l'explosion combinatoire
        return list(set(regions))[:3]
    
    def _execute_scenario_on_grid(self, scenario: str, input_grid: np.ndarray) -> Optional[np.ndarray]:
        """Exécute un scénario sur une grille"""
        try:
            h, w = input_grid.shape
            commands = [f"INIT {w} {h}", scenario]
            
            # Créer un nouvel exécuteur pour éviter les interférences
            executor = CommandExecutor()
            executor.grid = input_grid.copy()
            executor.width = w
            executor.height = h
            
            result_dict = executor.execute_commands(commands)
            
            if result_dict["success"] and result_dict["grid"] is not None:
                return np.array(result_dict["grid"])
            else:
                return None
                
        except Exception:
            return None
    
    def get_resolver_stats(self) -> Dict[str, Any]:
        """Retourne des statistiques sur le résolveur (pour debugging)"""
        return {
            'method': 'algorithmic_search',
            'timeout_seconds': self.timeout_seconds,
            'search_strategy': 'brute_force_optimized',
            'early_stopping': True,
            'note': 'Recherche algorithmique par force brute, pas d\'apprentissage automatique'
        }
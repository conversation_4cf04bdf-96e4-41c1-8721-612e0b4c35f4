"""
PatternAnalyzer pour la détection algorithmique de patterns.

Détection de patterns par calculs algorithmiques explicites.
Chaque 'détection' est un algorithme programmé, pas de l'apprentissage.
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from scipy import ndimage
from collections import Counter
import time


class PatternAnalyzer:
    """
    Détection de patterns par calculs algorithmiques explicites.
    Chaque 'détection' est un algorithme programmé, pas de l'apprentissage.
    """
    
    def __init__(self):
        """Initialise l'analyseur de patterns avec des seuils algorithmiques."""
        self.rotation_threshold = 0.95  # Seuil de similarité pour détecter rotations
        self.symmetry_threshold = 0.95  # Seuil de similarité pour détecter symétries
        self.color_change_threshold = 0.1  # Seuil pour détecter changements de couleurs
        
    def analyze_transformations(self, input_grid: np.ndarray, output_grid: np.ndarray) -> Dict[str, Any]:
        """
        Analyse par calculs géométriques :
        - Rotations : comparaison matricielle (rot90, rot180, rot270)
        - Symétries : calculs d'axes (horizontal, vertical, diagonal)
        - Couleurs : histogrammes et comparaisons
        - Motifs : corrélation spatiale
        """
        start_time = time.time()
        
        transformations = {
            'rotation': self._detect_rotation(input_grid, output_grid),
            'symmetry': self._detect_symmetry(input_grid, output_grid),
            'color_changes': self._analyze_color_changes(input_grid, output_grid),
            'size_change': self._detect_size_change(input_grid, output_grid),
            'copy_paste': self._detect_copy_paste(input_grid, output_grid),
            'fill_pattern': self._detect_fill_pattern(input_grid, output_grid)
        }
        
        execution_time = (time.time() - start_time) * 1000  # en millisecondes
        
        return {
            'transformations': transformations,
            'execution_time_ms': execution_time,
            'method': 'algorithmic_calculation'  # Pas d'IA
        }
    
    def categorize_puzzle(self, train_examples: List[Tuple[np.ndarray, np.ndarray]]) -> str:
        """
        Classification par règles explicites :
        - geometric_transform : si rotation/symétrie détectée
        - color_pattern : si changements de couleurs
        - resize_operation : si changement de taille
        - copy_paste : si motifs répétés
        - fill_pattern : si remplissage détecté
        """
        if not train_examples:
            return "unknown"
        
        # Analyser tous les exemples pour détecter des patterns consistants
        rotation_count = 0
        symmetry_count = 0
        color_change_count = 0
        size_change_count = 0
        copy_paste_count = 0
        fill_pattern_count = 0
        
        for input_grid, output_grid in train_examples:
            analysis = self.analyze_transformations(input_grid, output_grid)
            transforms = analysis['transformations']
            
            if transforms['rotation'] is not None:
                rotation_count += 1
            if transforms['symmetry'] is not None:
                symmetry_count += 1
            if len(transforms['color_changes']) > 0:
                color_change_count += 1
            if transforms['size_change']:
                size_change_count += 1
            if transforms['copy_paste']:
                copy_paste_count += 1
            if transforms['fill_pattern']:
                fill_pattern_count += 1
        
        total_examples = len(train_examples)
        
        # Classification par règles conditionnelles explicites
        # Priorité aux changements structurels (taille, géométrie)
        if size_change_count >= total_examples * 0.5:
            return "resize_operation"
        elif rotation_count >= total_examples * 0.5 or symmetry_count >= total_examples * 0.5:
            return "geometric_transform"
        elif color_change_count >= total_examples * 0.5:
            return "color_pattern"
        elif copy_paste_count >= total_examples * 0.5:
            return "copy_paste"
        elif fill_pattern_count >= total_examples * 0.5:
            return "fill_pattern"
        else:
            return "complex_pattern"
    
    def _detect_rotation(self, input_grid: np.ndarray, output_grid: np.ndarray) -> Optional[int]:
        """Comparaison matricielle pour détecter rotations 90°, 180°, 270°"""
        if input_grid.shape != output_grid.shape:
            return None
        
        # Tester les rotations de 90°, 180°, 270°
        for angle in [90, 180, 270]:
            rotated = np.rot90(input_grid, k=angle//90)
            similarity = self._calculate_similarity(rotated, output_grid)
            
            if similarity >= self.rotation_threshold:
                return angle
        
        return None
    
    def _detect_symmetry(self, input_grid: np.ndarray, output_grid: np.ndarray) -> Optional[str]:
        """Calculs d'axes de symétrie"""
        if input_grid.shape != output_grid.shape:
            return None
        
        # Test symétrie horizontale
        flipped_h = np.flipud(input_grid)
        if self._calculate_similarity(flipped_h, output_grid) >= self.symmetry_threshold:
            return "horizontal"
        
        # Test symétrie verticale
        flipped_v = np.fliplr(input_grid)
        if self._calculate_similarity(flipped_v, output_grid) >= self.symmetry_threshold:
            return "vertical"
        
        # Test symétrie diagonale (transposition)
        if input_grid.shape[0] == input_grid.shape[1]:  # Grille carrée seulement
            transposed = input_grid.T
            if self._calculate_similarity(transposed, output_grid) >= self.symmetry_threshold:
                return "diagonal"
        
        return None
    
    def _analyze_color_changes(self, input_grid: np.ndarray, output_grid: np.ndarray) -> Dict[int, int]:
        """Histogrammes de couleurs et mapping"""
        input_colors = Counter(input_grid.flatten())
        output_colors = Counter(output_grid.flatten())
        
        color_mapping = {}
        
        # Détecter les changements de couleurs par comparaison d'histogrammes
        for input_color, input_count in input_colors.items():
            # Chercher la couleur de sortie la plus probable
            best_match = None
            best_score = 0
            
            for output_color, output_count in output_colors.items():
                # Score basé sur la similarité des comptes
                score = min(input_count, output_count) / max(input_count, output_count)
                if score > best_score and score > self.color_change_threshold:
                    best_score = score
                    best_match = output_color
            
            if best_match is not None and input_color != best_match:
                color_mapping[input_color] = best_match
        
        return color_mapping
    
    def _detect_size_change(self, input_grid: np.ndarray, output_grid: np.ndarray) -> bool:
        """Détection de changement de taille par comparaison des dimensions"""
        return input_grid.shape != output_grid.shape
    
    def _detect_copy_paste(self, input_grid: np.ndarray, output_grid: np.ndarray) -> bool:
        """Détection de motifs répétés par corrélation spatiale"""
        if input_grid.shape != output_grid.shape:
            return False
        
        # Chercher des régions de l'input qui apparaissent plusieurs fois dans l'output
        h, w = input_grid.shape
        
        # Tester différentes tailles de motifs (2x2, 3x3, etc.)
        for size in range(2, min(h, w) // 2 + 1):
            for i in range(h - size + 1):
                for j in range(w - size + 1):
                    pattern = input_grid[i:i+size, j:j+size]
                    
                    # Compter les occurrences de ce pattern dans l'output
                    occurrences = self._count_pattern_occurrences(output_grid, pattern)
                    
                    if occurrences > 1:
                        return True
        
        return False
    
    def _detect_fill_pattern(self, input_grid: np.ndarray, output_grid: np.ndarray) -> bool:
        """Détection d'opérations de remplissage"""
        if input_grid.shape != output_grid.shape:
            return False
        
        # Détecter si des régions vides (couleur 0) ont été remplies
        input_zeros = np.sum(input_grid == 0)
        output_zeros = np.sum(output_grid == 0)
        
        # Si le nombre de zéros a significativement diminué, c'est probablement un remplissage
        if input_zeros > 0 and output_zeros < input_zeros * 0.5:
            return True
        
        # Détecter si une couleur dominante a été appliquée
        input_colors = Counter(input_grid.flatten())
        output_colors = Counter(output_grid.flatten())
        
        # Chercher une couleur qui a beaucoup augmenté
        for color, output_count in output_colors.items():
            input_count = input_colors.get(color, 0)
            if output_count > input_count * 2:  # La couleur a au moins doublé
                return True
        
        return False
    
    def _calculate_similarity(self, grid1: np.ndarray, grid2: np.ndarray) -> float:
        """Calcule la similarité entre deux grilles (0.0 à 1.0)"""
        if grid1.shape != grid2.shape:
            return 0.0
        
        matching_pixels = np.sum(grid1 == grid2)
        total_pixels = grid1.size
        
        return matching_pixels / total_pixels
    
    def _count_pattern_occurrences(self, grid: np.ndarray, pattern: np.ndarray) -> int:
        """Compte les occurrences d'un pattern dans une grille"""
        if pattern.shape[0] > grid.shape[0] or pattern.shape[1] > grid.shape[1]:
            return 0
        
        count = 0
        h, w = grid.shape
        ph, pw = pattern.shape
        
        for i in range(h - ph + 1):
            for j in range(w - pw + 1):
                if np.array_equal(grid[i:i+ph, j:j+pw], pattern):
                    count += 1
        
        return count
    
    def get_analysis_stats(self) -> Dict[str, Any]:
        """Retourne des statistiques sur l'analyseur (pour debugging)"""
        return {
            'method': 'algorithmic_pattern_detection',
            'rotation_threshold': self.rotation_threshold,
            'symmetry_threshold': self.symmetry_threshold,
            'color_change_threshold': self.color_change_threshold,
            'supported_transformations': [
                'rotation', 'symmetry', 'color_changes', 
                'size_change', 'copy_paste', 'fill_pattern'
            ],
            'note': 'Tous les calculs sont algorithmiques, pas d\'apprentissage automatique'
        }
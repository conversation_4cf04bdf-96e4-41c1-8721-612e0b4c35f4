"""
Système de métriques honnêtes pour le pipeline ARC-Solver.

Ce module calcule des métriques transparentes sur les performances algorithmiques
du pipeline, sans prétendre à des capacités d'IA inexistantes.
Toutes les métriques sont basées sur des calculs statistiques explicites.
"""

from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import numpy as np
import json
from pathlib import Path
import time

from data_structures import (
    PipelineResult, AnalysisResult, TemplateResult, SolutionResult,
    PipelineConfig
)


@dataclass
class MetricsReport:
    """
    Rapport de métriques avec transparence sur les méthodes utilisées.
    
    Toutes les métriques sont calculées par des algorithmes statistiques
    explicites, pas par apprentissage automatique.
    """
    # Métriques principales
    categorization_accuracy: float  # % puzzles correctement classés
    resolution_rate: float          # % paramètres trouvés
    average_execution_time_ms: float # Temps moyen algorithmique
    template_coverage: float        # % puzzles couverts par templates
    
    # Détails par catégorie
    category_breakdown: Dict[str, Dict[str, Any]]
    
    # Métriques de performance
    timeout_rate: float             # % puzzles avec timeout
    error_rate: float              # % puzzles avec erreurs
    
    # Transparence sur les méthodes
    total_puzzles_tested: int
    successful_resolutions: int
    method_transparency: Dict[str, str]
    
    # Validation de l'objectif 30%
    meets_target_resolution: bool
    
    def to_dict(self) -> Dict[str, Any]:
        """Conversion en dictionnaire pour sauvegarde."""
        return {
            "categorization_accuracy": self.categorization_accuracy,
            "resolution_rate": self.resolution_rate,
            "average_execution_time_ms": self.average_execution_time_ms,
            "template_coverage": self.template_coverage,
            "category_breakdown": self.category_breakdown,
            "timeout_rate": self.timeout_rate,
            "error_rate": self.error_rate,
            "total_puzzles_tested": self.total_puzzles_tested,
            "successful_resolutions": self.successful_resolutions,
            "method_transparency": self.method_transparency,
            "meets_target_resolution": self.meets_target_resolution,
            "generation_timestamp": time.time(),
            "transparency_note": "Toutes les métriques sont calculées par des algorithmes statistiques explicites"
        }


class MetricsCalculator:
    """
    Calculateur de métriques basé sur des algorithmes statistiques explicites.
    
    Chaque métrique est calculée par des formules mathématiques transparentes,
    sans prétendre à des capacités d'analyse automatique.
    """
    
    def __init__(self):
        self.results_history: List[PipelineResult] = []
        self.category_stats: Dict[str, Dict[str, int]] = {}
        self.execution_times: List[float] = []
    
    def add_result(self, result: PipelineResult):
        """
        Ajouter un résultat pour le calcul des métriques.
        
        Args:
            result: Résultat d'exécution du pipeline
        """
        self.results_history.append(result)
        self.execution_times.append(result.total_execution_time_ms)
        
        # Mise à jour des statistiques par catégorie
        if result.analysis_result:
            category = result.analysis_result.category
            if category not in self.category_stats:
                self.category_stats[category] = {
                    "total": 0, "successful": 0, "failed": 0, "timeout": 0
                }
            
            self.category_stats[category]["total"] += 1
            
            if result.success:
                self.category_stats[category]["successful"] += 1
            elif result.error_step == "timeout_exceeded":
                self.category_stats[category]["timeout"] += 1
            else:
                self.category_stats[category]["failed"] += 1
    
    def calculate_categorization_accuracy(self) -> float:
        """
        Calculer la précision de catégorisation par comptage statistique.
        
        Méthode : Compter les puzzles correctement catégorisés / total
        (Algorithme de comptage, pas d'apprentissage automatique)
        
        Returns:
            Pourcentage de puzzles correctement catégorisés
        """
        if not self.results_history:
            return 0.0
        
        correctly_categorized = 0
        total_with_analysis = 0
        
        for result in self.results_history:
            if result.analysis_result:
                total_with_analysis += 1
                # Un puzzle est "correctement catégorisé" si l'analyse a mené à un template
                if result.template_result:
                    correctly_categorized += 1
        
        if total_with_analysis == 0:
            return 0.0
        
        return (correctly_categorized / total_with_analysis) * 100.0
    
    def calculate_resolution_rate(self) -> float:
        """
        Calculer le taux de résolution par comptage algorithmique.
        
        Méthode : Compter les solutions trouvées / total des tentatives
        (Calcul statistique direct, pas d'inférence IA)
        
        Returns:
            Pourcentage de paramètres résolus avec succès
        """
        if not self.results_history:
            return 0.0
        
        successful_resolutions = sum(1 for result in self.results_history if result.success)
        total_attempts = len(self.results_history)
        
        return (successful_resolutions / total_attempts) * 100.0
    
    def calculate_average_execution_time(self) -> float:
        """
        Calculer le temps d'exécution moyen par calcul arithmétique.
        
        Méthode : Somme des temps / nombre d'exécutions
        (Calcul mathématique simple, pas d'optimisation automatique)
        
        Returns:
            Temps d'exécution moyen en millisecondes
        """
        if not self.execution_times:
            return 0.0
        
        return sum(self.execution_times) / len(self.execution_times)
    
    def calculate_template_coverage(self) -> float:
        """
        Calculer la couverture des templates par comptage direct.
        
        Méthode : Compter les puzzles avec template généré / total
        (Algorithme de comptage, pas d'analyse sémantique)
        
        Returns:
            Pourcentage de puzzles couverts par les templates
        """
        if not self.results_history:
            return 0.0
        
        with_template = sum(1 for result in self.results_history 
                          if result.template_result is not None)
        total = len(self.results_history)
        
        return (with_template / total) * 100.0
    
    def calculate_timeout_rate(self) -> float:
        """
        Calculer le taux de timeout par comptage statistique.
        
        Returns:
            Pourcentage de puzzles ayant dépassé le timeout
        """
        if not self.results_history:
            return 0.0
        
        timeouts = sum(1 for result in self.results_history 
                      if result.error_step == "timeout_exceeded")
        total = len(self.results_history)
        
        return (timeouts / total) * 100.0
    
    def calculate_error_rate(self) -> float:
        """
        Calculer le taux d'erreur par comptage direct.
        
        Returns:
            Pourcentage de puzzles ayant échoué avec erreur
        """
        if not self.results_history:
            return 0.0
        
        errors = sum(1 for result in self.results_history if not result.success)
        total = len(self.results_history)
        
        return (errors / total) * 100.0
    
    def generate_category_breakdown(self) -> Dict[str, Dict[str, Any]]:
        """
        Générer une analyse détaillée par catégorie de puzzle.
        
        Returns:
            Dictionnaire avec statistiques par catégorie
        """
        breakdown = {}
        
        for category, stats in self.category_stats.items():
            total = stats["total"]
            if total > 0:
                breakdown[category] = {
                    "total_puzzles": total,
                    "success_rate": (stats["successful"] / total) * 100.0,
                    "failure_rate": (stats["failed"] / total) * 100.0,
                    "timeout_rate": (stats["timeout"] / total) * 100.0,
                    "method_used": "algorithmic_pattern_detection"  # Transparence
                }
        
        return breakdown
    
    def generate_report(self) -> MetricsReport:
        """
        Générer un rapport complet de métriques honnêtes.
        
        Returns:
            Rapport avec toutes les métriques calculées
        """
        if not self.results_history:
            return MetricsReport(
                categorization_accuracy=0.0,
                resolution_rate=0.0,
                average_execution_time_ms=0.0,
                template_coverage=0.0,
                category_breakdown={},
                timeout_rate=0.0,
                error_rate=0.0,
                total_puzzles_tested=0,
                successful_resolutions=0,
                method_transparency={
                    "categorization": "geometric_calculations_and_histograms",
                    "template_generation": "rule_based_substitution",
                    "parameter_resolution": "brute_force_search",
                    "validation": "direct_execution_comparison"
                },
                meets_target_resolution=False
            )
        
        resolution_rate = self.calculate_resolution_rate()
        
        return MetricsReport(
            categorization_accuracy=self.calculate_categorization_accuracy(),
            resolution_rate=resolution_rate,
            average_execution_time_ms=self.calculate_average_execution_time(),
            template_coverage=self.calculate_template_coverage(),
            category_breakdown=self.generate_category_breakdown(),
            timeout_rate=self.calculate_timeout_rate(),
            error_rate=self.calculate_error_rate(),
            total_puzzles_tested=len(self.results_history),
            successful_resolutions=sum(1 for r in self.results_history if r.success),
            method_transparency={
                "categorization": "geometric_calculations_and_histograms",
                "template_generation": "rule_based_substitution", 
                "parameter_resolution": "brute_force_search",
                "validation": "direct_execution_comparison"
            },
            meets_target_resolution=resolution_rate >= (PipelineConfig.TARGET_RESOLUTION_RATE * 100)
        )


class MetricsReporter:
    """
    Générateur de rapports de métriques avec transparence technique.
    
    Produit des rapports honnêtes sur les capacités réelles du système
    sans embellir les performances algorithmiques.
    """
    
    def __init__(self, output_dir: str = "metrics_reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
    
    def save_report(self, report: MetricsReport, filename: Optional[str] = None) -> str:
        """
        Sauvegarder un rapport de métriques.
        
        Args:
            report: Rapport à sauvegarder
            filename: Nom du fichier (optionnel)
        
        Returns:
            Chemin du fichier sauvegardé
        """
        if filename is None:
            timestamp = int(time.time())
            filename = f"metrics_report_{timestamp}.json"
        
        filepath = self.output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report.to_dict(), f, indent=2, ensure_ascii=False)
        
        return str(filepath)
    
    def generate_text_report(self, report: MetricsReport) -> str:
        """
        Générer un rapport textuel lisible avec transparence technique.
        
        Args:
            report: Rapport de métriques
        
        Returns:
            Rapport formaté en texte
        """
        lines = [
            "=" * 60,
            "RAPPORT DE MÉTRIQUES ARC-SOLVER PIPELINE",
            "=" * 60,
            "",
            "🔍 TRANSPARENCE TECHNIQUE",
            f"Toutes les métriques sont calculées par des algorithmes statistiques explicites.",
            f"Aucune prétention à des capacités d'IA inexistantes.",
            "",
            "📊 MÉTRIQUES PRINCIPALES",
            f"• Précision de catégorisation : {report.categorization_accuracy:.1f}%",
            f"  (Calcul : puzzles avec template généré / total analysé)",
            f"• Taux de résolution : {report.resolution_rate:.1f}%",
            f"  (Calcul : solutions trouvées / total des tentatives)",
            f"• Temps d'exécution moyen : {report.average_execution_time_ms:.0f}ms",
            f"  (Calcul : somme des temps / nombre d'exécutions)",
            f"• Couverture des templates : {report.template_coverage:.1f}%",
            f"  (Calcul : puzzles avec template / total)",
            "",
            "⚡ MÉTRIQUES DE PERFORMANCE",
            f"• Taux de timeout : {report.timeout_rate:.1f}%",
            f"• Taux d'erreur : {report.error_rate:.1f}%",
            f"• Puzzles testés : {report.total_puzzles_tested}",
            f"• Résolutions réussies : {report.successful_resolutions}",
            "",
            f"🎯 OBJECTIF 30% DE RÉSOLUTION",
            f"• Objectif atteint : {'✅ OUI' if report.meets_target_resolution else '❌ NON'}",
            f"• Taux actuel : {report.resolution_rate:.1f}%",
            f"• Écart : {report.resolution_rate - 30.0:+.1f} points",
            "",
            "🔧 MÉTHODES UTILISÉES (TRANSPARENCE)",
        ]
        
        for component, method in report.method_transparency.items():
            lines.append(f"• {component.title()} : {method}")
        
        if report.category_breakdown:
            lines.extend([
                "",
                "📋 ANALYSE PAR CATÉGORIE",
            ])
            
            for category, stats in report.category_breakdown.items():
                lines.extend([
                    f"",
                    f"Catégorie : {category}",
                    f"  • Puzzles : {stats['total_puzzles']}",
                    f"  • Succès : {stats['success_rate']:.1f}%",
                    f"  • Échecs : {stats['failure_rate']:.1f}%",
                    f"  • Timeouts : {stats['timeout_rate']:.1f}%",
                    f"  • Méthode : {stats['method_used']}"
                ])
        
        lines.extend([
            "",
            "=" * 60,
            "NOTES IMPORTANTES",
            "=" * 60,
            "• Ce système utilise des calculs programmés, pas d'apprentissage automatique",
            "• Les 'détections' sont des comparaisons matricielles et calculs géométriques",
            "• La 'résolution' est une recherche par force brute avec énumération",
            "• Les performances reflètent l'efficacité algorithmique, pas l'intelligence",
            f"• Objectif réaliste : 30% de puzzles résolus sur un sous-ensemble simple",
            "=" * 60
        ])
        
        return "\n".join(lines)
    
    def save_text_report(self, report: MetricsReport, filename: Optional[str] = None) -> str:
        """
        Sauvegarder un rapport textuel.
        
        Args:
            report: Rapport de métriques
            filename: Nom du fichier (optionnel)
        
        Returns:
            Chemin du fichier sauvegardé
        """
        if filename is None:
            timestamp = int(time.time())
            filename = f"metrics_report_{timestamp}.txt"
        
        filepath = self.output_dir / filename
        text_report = self.generate_text_report(report)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(text_report)
        
        return str(filepath)


def validate_target_resolution(report: MetricsReport) -> Dict[str, Any]:
    """
    Valider si l'objectif de 30% de puzzles résolus est atteint.
    
    Args:
        report: Rapport de métriques
    
    Returns:
        Dictionnaire avec résultat de validation
    """
    target = PipelineConfig.TARGET_RESOLUTION_RATE * 100  # 30%
    current = report.resolution_rate
    
    return {
        "target_percentage": target,
        "current_percentage": current,
        "meets_target": current >= target,
        "gap": current - target,
        "status": "FUNCTIONAL" if current >= target else "NEEDS_IMPROVEMENT",
        "recommendation": (
            "Le système atteint l'objectif de fonctionnalité" if current >= target
            else f"Améliorer les algorithmes pour gagner {target - current:.1f} points"
        ),
        "method_note": "Validation par calcul statistique direct, pas par évaluation IA"
    }


def compare_metrics_over_time(reports: List[MetricsReport]) -> Dict[str, Any]:
    """
    Comparer l'évolution des métriques dans le temps.
    
    Args:
        reports: Liste de rapports chronologiques
    
    Returns:
        Analyse de l'évolution des performances
    """
    if len(reports) < 2:
        return {"error": "Au moins 2 rapports nécessaires pour la comparaison"}
    
    first = reports[0]
    last = reports[-1]
    
    return {
        "resolution_rate_evolution": {
            "initial": first.resolution_rate,
            "final": last.resolution_rate,
            "change": last.resolution_rate - first.resolution_rate,
            "trend": "IMPROVEMENT" if last.resolution_rate > first.resolution_rate else "DECLINE"
        },
        "execution_time_evolution": {
            "initial": first.average_execution_time_ms,
            "final": last.average_execution_time_ms,
            "change": last.average_execution_time_ms - first.average_execution_time_ms,
            "trend": "FASTER" if last.average_execution_time_ms < first.average_execution_time_ms else "SLOWER"
        },
        "template_coverage_evolution": {
            "initial": first.template_coverage,
            "final": last.template_coverage,
            "change": last.template_coverage - first.template_coverage
        },
        "analysis_note": "Comparaison par calculs arithmétiques simples, pas par analyse prédictive"
    }
"""
Script de validation complète pour le pipeline ARC-Solver.

Exécute tous les tests de performance, qualité et régression
pour valider que le système atteint les objectifs fixés.
Tous les tests sont basés sur des calculs algorithmiques explicites.
"""

import sys
import time
import json
from pathlib import Path
from typing import Dict, Any

# Ajouter le répertoire tests au path
sys.path.append('tests')

# Importer directement depuis les fichiers tests
sys.path.insert(0, 'tests')
from test_performance_validation import PerformanceValidator, save_validation_report
from test_regression_suite import RegressionTestSuite
from test_quality_validation import QualityValidator, save_quality_report


class CompleteValidator:
    """
    Validateur complet pour le pipeline ARC-Solver.
    
    Orchestre tous les types de tests pour une validation finale
    du système avant déploiement.
    """
    
    def __init__(self):
        self.timestamp = int(time.time())
        self.results = {}
    
    def run_regression_tests(self) -> Dict[str, Any]:
        """
        Exécuter les tests de régression.
        
        Returns:
            Résultats des tests de régression
        """
        print("=" * 60)
        print("1. TESTS DE RÉGRESSION")
        print("=" * 60)
        
        suite = RegressionTestSuite()
        results = suite.run_all_tests()
        
        return results
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """
        Exécuter les tests de performance.
        
        Returns:
            Résultats des tests de performance
        """
        print("\n" + "=" * 60)
        print("2. TESTS DE PERFORMANCE")
        print("=" * 60)
        
        validator = PerformanceValidator()
        results = validator.run_full_validation()
        
        return results
    
    def run_quality_tests(self) -> Dict[str, Any]:
        """
        Exécuter les tests de qualité.
        
        Returns:
            Résultats des tests de qualité
        """
        print("\n" + "=" * 60)
        print("3. TESTS DE QUALITÉ")
        print("=" * 60)
        
        validator = QualityValidator()
        results = validator.run_quality_validation()
        
        return results
    
    def generate_comprehensive_report(self, regression_results: Dict, 
                                    performance_results: Dict, 
                                    quality_results: Dict) -> Dict[str, Any]:
        """
        Générer un rapport complet de validation.
        
        Args:
            regression_results: Résultats des tests de régression
            performance_results: Résultats des tests de performance
            quality_results: Résultats des tests de qualité
        
        Returns:
            Rapport complet de validation
        """
        # Analyser les résultats globaux
        regression_passed = regression_results["overall_status"] == "PASS"
        performance_passed = performance_results["overall_assessment"]["overall_success"]
        quality_passed = quality_results["overall_quality_assessment"]["meets_30_percent_objective"]
        
        all_tests_passed = regression_passed and performance_passed and quality_passed
        
        # Compiler les métriques clés
        key_metrics = {
            "regression_success_rate": regression_results["success_rate"],
            "performance_grade": performance_results["overall_assessment"]["performance_grade"],
            "quality_grade": quality_results["overall_quality_assessment"]["quality_grade"],
            "resolution_rate": quality_results["objective_validation"]["resolution_rate"],
            "meets_30_percent_target": quality_passed,
            "system_consistency": quality_results["consistency_validation"]["is_consistent"],
            "average_execution_time": performance_results["execution_time_tests"]["avg_time_ms"]
        }
        
        # Générer les recommandations globales
        global_recommendations = []
        
        if not regression_passed:
            global_recommendations.append("Corriger les régressions détectées avant déploiement")
        
        if not performance_passed:
            global_recommendations.extend(
                performance_results["overall_assessment"]["recommendations"]
            )
        
        if not quality_passed:
            global_recommendations.extend(
                quality_results["overall_quality_assessment"]["recommendations"]
            )
        
        if all_tests_passed:
            global_recommendations.append("Système validé - Prêt pour extension à plus de puzzles ARC")
        
        # Déterminer le statut final
        if all_tests_passed:
            final_status = "VALIDATED"
            final_grade = "PASS"
        elif quality_passed:  # Au minimum l'objectif de qualité doit être atteint
            final_status = "CONDITIONALLY_VALIDATED"
            final_grade = "CONDITIONAL_PASS"
        else:
            final_status = "NOT_VALIDATED"
            final_grade = "FAIL"
        
        return {
            "timestamp": self.timestamp,
            "validation_summary": {
                "final_status": final_status,
                "final_grade": final_grade,
                "all_tests_passed": all_tests_passed,
                "regression_passed": regression_passed,
                "performance_passed": performance_passed,
                "quality_passed": quality_passed
            },
            "key_metrics": key_metrics,
            "detailed_results": {
                "regression_tests": regression_results,
                "performance_tests": performance_results,
                "quality_tests": quality_results
            },
            "global_recommendations": global_recommendations,
            "transparency_statement": (
                "Tous les tests basés sur des calculs algorithmiques explicites. "
                "Aucune prétention à des capacités d'IA inexistantes. "
                "Le système utilise des algorithmes programmés pour l'analyse de patterns "
                "et la recherche de paramètres par force brute."
            )
        }
    
    def save_comprehensive_report(self, report: Dict[str, Any]) -> str:
        """
        Sauvegarder le rapport complet.
        
        Args:
            report: Rapport complet à sauvegarder
        
        Returns:
            Chemin du fichier sauvegardé
        """
        reports_dir = Path("validation_reports")
        reports_dir.mkdir(exist_ok=True)
        
        filename = f"complete_validation_{self.timestamp}.json"
        filepath = reports_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return str(filepath)
    
    def print_final_summary(self, report: Dict[str, Any]):
        """
        Afficher le résumé final de validation.
        
        Args:
            report: Rapport complet de validation
        """
        print("\n" + "=" * 60)
        print("RÉSUMÉ FINAL DE VALIDATION")
        print("=" * 60)
        
        summary = report["validation_summary"]
        metrics = report["key_metrics"]
        
        print(f"Statut final: {summary['final_status']}")
        print(f"Grade global: {summary['final_grade']}")
        print(f"Tous les tests réussis: {summary['all_tests_passed']}")
        
        print(f"\nMétriques clés:")
        print(f"• Taux de régression: {metrics['regression_success_rate']:.1f}%")
        print(f"• Grade de performance: {metrics['performance_grade']}")
        print(f"• Grade de qualité: {metrics['quality_grade']}")
        print(f"• Taux de résolution: {metrics['resolution_rate']:.1f}%")
        print(f"• Objectif 30% atteint: {metrics['meets_30_percent_target']}")
        print(f"• Temps d'exécution moyen: {metrics['average_execution_time']:.1f}ms")
        
        print(f"\nDétail des tests:")
        print(f"• Tests de régression: {'✅ PASS' if summary['regression_passed'] else '❌ FAIL'}")
        print(f"• Tests de performance: {'✅ PASS' if summary['performance_passed'] else '❌ FAIL'}")
        print(f"• Tests de qualité: {'✅ PASS' if summary['quality_passed'] else '❌ FAIL'}")
        
        if report["global_recommendations"]:
            print(f"\nRecommandations globales:")
            for rec in report["global_recommendations"]:
                print(f"• {rec}")
        
        print(f"\nTransparence technique:")
        print(f"• {report['transparency_statement']}")
    
    def run_complete_validation(self) -> bool:
        """
        Exécuter la validation complète du système.
        
        Returns:
            True si la validation est réussie
        """
        print("VALIDATION COMPLÈTE DU PIPELINE ARC-SOLVER")
        print("Système de résolution ARC basé sur des algorithmes explicites")
        print(f"Timestamp: {self.timestamp}")
        
        try:
            # 1. Tests de régression
            regression_results = self.run_regression_tests()
            
            # 2. Tests de performance
            performance_results = self.run_performance_tests()
            
            # 3. Tests de qualité
            quality_results = self.run_quality_tests()
            
            # 4. Générer le rapport complet
            comprehensive_report = self.generate_comprehensive_report(
                regression_results, performance_results, quality_results
            )
            
            # 5. Sauvegarder le rapport
            report_path = self.save_comprehensive_report(comprehensive_report)
            print(f"\n✓ Rapport complet sauvegardé: {report_path}")
            
            # 6. Sauvegarder aussi les rapports individuels
            perf_path = save_validation_report(
                performance_results, 
                f"performance_validation_{self.timestamp}.json"
            )
            quality_path = save_quality_report(
                quality_results,
                f"quality_validation_{self.timestamp}.json"
            )
            
            print(f"✓ Rapport de performance: {perf_path}")
            print(f"✓ Rapport de qualité: {quality_path}")
            
            # 7. Afficher le résumé final
            self.print_final_summary(comprehensive_report)
            
            # 8. Retourner le statut de validation
            return comprehensive_report["validation_summary"]["quality_passed"]
            
        except Exception as e:
            print(f"\n❌ ERREUR LORS DE LA VALIDATION COMPLÈTE: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Point d'entrée principal."""
    validator = CompleteValidator()
    success = validator.run_complete_validation()
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
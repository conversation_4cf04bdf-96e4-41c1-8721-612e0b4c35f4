"""
Tests unitaires pour le système de métriques honnêtes du pipeline ARC-Solver.

Valide que toutes les métriques sont calculées correctement par des algorithmes
statistiques explicites, sans prétendre à des capacités d'IA inexistantes.
"""

import pytest
import tempfile
import json
from pathlib import Path
import numpy as np

from src.metrics_system import (
    MetricsReport, MetricsCalculator, MetricsReporter,
    validate_target_resolution, compare_metrics_over_time
)
from src.data_structures import (
    PipelineResult, AnalysisResult, TemplateResult, SolutionResult
)


class TestMetricsCalculator:
    """Tests pour le calculateur de métriques."""
    
    def setup_method(self):
        """Initialisation pour chaque test."""
        self.calculator = MetricsCalculator()
    
    def create_successful_result(self, puzzle_id: str, category: str = "geometric_transform") -> PipelineResult:
        """Créer un résultat de pipeline réussi pour les tests."""
        analysis = AnalysisResult(
            category=category,
            confidence=0.8,
            detected_transformations=["rotation"],
            analysis_details={},
            execution_time_ms=100.0
        )
        
        template = TemplateResult(
            template="ROTATE {angle} {center}",
            variables={"angle": [90], "center": ["5,5"]},
            constraints={},
            generation_method="rule_based",
            category_used=category
        )
        
        solution = SolutionResult(
            success=True,
            solution_command="ROTATE 90 5,5",
            execution_time_ms=1000.0,
            validation_score=1.0,
            error_message=None,
            method_used="algorithmic_search",
            parameters_tested=50,
            timeout_reached=False
        )
        
        return PipelineResult(
            puzzle_id=puzzle_id,
            success=True,
            final_solution="ROTATE 90 5,5",
            analysis_result=analysis,
            template_result=template,
            solution_result=solution,
            total_execution_time_ms=1200.0,
            error_step=None,
            error_message=None
        )
    
    def create_failed_result(self, puzzle_id: str, error_step: str = "parameter_search_failed") -> PipelineResult:
        """Créer un résultat de pipeline échoué pour les tests."""
        analysis = AnalysisResult(
            category="unknown",
            confidence=0.2,
            detected_transformations=[],
            analysis_details={},
            execution_time_ms=100.0
        )
        
        return PipelineResult(
            puzzle_id=puzzle_id,
            success=False,
            final_solution=None,
            analysis_result=analysis,
            template_result=None,
            solution_result=None,
            total_execution_time_ms=500.0,
            error_step=error_step,
            error_message="Échec de la recherche algorithmique"
        )
    
    def test_empty_calculator(self):
        """Test du calculateur sans données."""
        assert self.calculator.calculate_categorization_accuracy() == 0.0
        assert self.calculator.calculate_resolution_rate() == 0.0
        assert self.calculator.calculate_average_execution_time() == 0.0
        assert self.calculator.calculate_template_coverage() == 0.0
    
    def test_add_successful_result(self):
        """Test d'ajout d'un résultat réussi."""
        result = self.create_successful_result("test_001")
        self.calculator.add_result(result)
        
        assert len(self.calculator.results_history) == 1
        assert len(self.calculator.execution_times) == 1
        assert "geometric_transform" in self.calculator.category_stats
    
    def test_categorization_accuracy_calculation(self):
        """Test du calcul de précision de catégorisation."""
        # Ajouter 3 résultats réussis et 1 échoué
        for i in range(3):
            result = self.create_successful_result(f"success_{i}")
            self.calculator.add_result(result)
        
        failed_result = self.create_failed_result("failed_001")
        self.calculator.add_result(failed_result)
        
        # 3 puzzles avec template sur 4 total = 75%
        accuracy = self.calculator.calculate_categorization_accuracy()
        assert accuracy == 75.0
    
    def test_resolution_rate_calculation(self):
        """Test du calcul du taux de résolution."""
        # Ajouter 2 résultats réussis et 3 échoués
        for i in range(2):
            result = self.create_successful_result(f"success_{i}")
            self.calculator.add_result(result)
        
        for i in range(3):
            result = self.create_failed_result(f"failed_{i}")
            self.calculator.add_result(result)
        
        # 2 succès sur 5 total = 40%
        rate = self.calculator.calculate_resolution_rate()
        assert rate == 40.0
    
    def test_average_execution_time_calculation(self):
        """Test du calcul du temps d'exécution moyen."""
        # Ajouter des résultats avec temps différents
        result1 = self.create_successful_result("test_001")
        result1.total_execution_time_ms = 1000.0
        self.calculator.add_result(result1)
        
        result2 = self.create_successful_result("test_002")
        result2.total_execution_time_ms = 2000.0
        self.calculator.add_result(result2)
        
        # Moyenne : (1000 + 2000) / 2 = 1500
        avg_time = self.calculator.calculate_average_execution_time()
        assert avg_time == 1500.0
    
    def test_template_coverage_calculation(self):
        """Test du calcul de couverture des templates."""
        # 2 résultats avec template, 1 sans template
        for i in range(2):
            result = self.create_successful_result(f"with_template_{i}")
            self.calculator.add_result(result)
        
        result_no_template = self.create_failed_result("no_template")
        result_no_template.template_result = None
        self.calculator.add_result(result_no_template)
        
        # 2 avec template sur 3 total = 66.67%
        coverage = self.calculator.calculate_template_coverage()
        assert abs(coverage - 66.67) < 0.01
    
    def test_timeout_rate_calculation(self):
        """Test du calcul du taux de timeout."""
        # 1 résultat avec timeout, 2 sans
        timeout_result = self.create_failed_result("timeout_001", "timeout_exceeded")
        self.calculator.add_result(timeout_result)
        
        for i in range(2):
            result = self.create_successful_result(f"success_{i}")
            self.calculator.add_result(result)
        
        # 1 timeout sur 3 total = 33.33%
        timeout_rate = self.calculator.calculate_timeout_rate()
        assert abs(timeout_rate - 33.33) < 0.01
    
    def test_error_rate_calculation(self):
        """Test du calcul du taux d'erreur."""
        # 2 erreurs, 1 succès
        for i in range(2):
            result = self.create_failed_result(f"error_{i}")
            self.calculator.add_result(result)
        
        success_result = self.create_successful_result("success_001")
        self.calculator.add_result(success_result)
        
        # 2 erreurs sur 3 total = 66.67%
        error_rate = self.calculator.calculate_error_rate()
        assert abs(error_rate - 66.67) < 0.01
    
    def test_category_breakdown_generation(self):
        """Test de génération de l'analyse par catégorie."""
        # Ajouter des résultats de différentes catégories
        geo_success = self.create_successful_result("geo_001", "geometric_transform")
        self.calculator.add_result(geo_success)
        
        color_failed = self.create_failed_result("color_001")
        color_failed.analysis_result.category = "color_pattern"
        self.calculator.add_result(color_failed)
        
        breakdown = self.calculator.generate_category_breakdown()
        
        assert "geometric_transform" in breakdown
        assert "color_pattern" in breakdown
        assert breakdown["geometric_transform"]["success_rate"] == 100.0
        assert breakdown["color_pattern"]["success_rate"] == 0.0
    
    def test_generate_complete_report(self):
        """Test de génération d'un rapport complet."""
        # Ajouter quelques résultats
        for i in range(3):
            result = self.create_successful_result(f"success_{i}")
            self.calculator.add_result(result)
        
        for i in range(2):
            result = self.create_failed_result(f"failed_{i}")
            self.calculator.add_result(result)
        
        report = self.calculator.generate_report()
        
        assert isinstance(report, MetricsReport)
        assert report.total_puzzles_tested == 5
        assert report.successful_resolutions == 3
        assert report.resolution_rate == 60.0
        assert "algorithmic_search" in report.method_transparency["parameter_resolution"]
        assert report.meets_target_resolution is True  # 60% > 30%


class TestMetricsReport:
    """Tests pour la structure MetricsReport."""
    
    def test_metrics_report_to_dict(self):
        """Test de conversion en dictionnaire."""
        report = MetricsReport(
            categorization_accuracy=85.0,
            resolution_rate=45.0,
            average_execution_time_ms=1500.0,
            template_coverage=70.0,
            category_breakdown={"test": {"total": 1}},
            timeout_rate=5.0,
            error_rate=10.0,
            total_puzzles_tested=100,
            successful_resolutions=45,
            method_transparency={"test": "algorithmic"},
            meets_target_resolution=True
        )
        
        report_dict = report.to_dict()
        
        assert report_dict["categorization_accuracy"] == 85.0
        assert report_dict["resolution_rate"] == 45.0
        assert "transparency_note" in report_dict
        assert "algorithmes programmés" in report_dict["transparency_note"]
        assert "generation_timestamp" in report_dict


class TestMetricsReporter:
    """Tests pour le générateur de rapports."""
    
    def setup_method(self):
        """Initialisation pour chaque test."""
        self.temp_dir = tempfile.mkdtemp()
        self.reporter = MetricsReporter(self.temp_dir)
    
    def create_sample_report(self) -> MetricsReport:
        """Créer un rapport d'exemple pour les tests."""
        return MetricsReport(
            categorization_accuracy=80.0,
            resolution_rate=35.0,
            average_execution_time_ms=2000.0,
            template_coverage=75.0,
            category_breakdown={
                "geometric_transform": {
                    "total_puzzles": 10,
                    "success_rate": 60.0,
                    "failure_rate": 30.0,
                    "timeout_rate": 10.0,
                    "method_used": "algorithmic_pattern_detection"
                }
            },
            timeout_rate=8.0,
            error_rate=15.0,
            total_puzzles_tested=50,
            successful_resolutions=17,
            method_transparency={
                "categorization": "geometric_calculations",
                "resolution": "brute_force_search"
            },
            meets_target_resolution=True
        )
    
    def test_save_json_report(self):
        """Test de sauvegarde d'un rapport JSON."""
        report = self.create_sample_report()
        filepath = self.reporter.save_report(report, "test_report.json")
        
        assert Path(filepath).exists()
        
        # Vérifier le contenu
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        assert data["categorization_accuracy"] == 80.0
        assert data["resolution_rate"] == 35.0
        assert "transparency_note" in data
    
    def test_generate_text_report(self):
        """Test de génération d'un rapport textuel."""
        report = self.create_sample_report()
        text_report = self.reporter.generate_text_report(report)
        
        assert "RAPPORT DE MÉTRIQUES ARC-SOLVER PIPELINE" in text_report
        assert "TRANSPARENCE TECHNIQUE" in text_report
        assert "algorithmes statistiques explicites" in text_report
        assert "Précision de catégorisation : 80.0%" in text_report
        assert "Taux de résolution : 35.0%" in text_report
        assert "OBJECTIF 30% DE RÉSOLUTION" in text_report
        assert "✅ OUI" in text_report  # Objectif atteint
        assert "calculs programmés" in text_report
    
    def test_save_text_report(self):
        """Test de sauvegarde d'un rapport textuel."""
        report = self.create_sample_report()
        filepath = self.reporter.save_text_report(report, "test_report.txt")
        
        assert Path(filepath).exists()
        
        # Vérifier le contenu
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        assert "MÉTRIQUES PRINCIPALES" in content
        assert "TRANSPARENCE TECHNIQUE" in content


class TestValidationFunctions:
    """Tests pour les fonctions de validation."""
    
    def test_validate_target_resolution_success(self):
        """Test de validation avec objectif atteint."""
        report = MetricsReport(
            categorization_accuracy=80.0,
            resolution_rate=45.0,  # > 30%
            average_execution_time_ms=1500.0,
            template_coverage=70.0,
            category_breakdown={},
            timeout_rate=5.0,
            error_rate=10.0,
            total_puzzles_tested=100,
            successful_resolutions=45,
            method_transparency={},
            meets_target_resolution=True
        )
        
        validation = validate_target_resolution(report)
        
        assert validation["meets_target"] is True
        assert validation["status"] == "FUNCTIONAL"
        assert validation["gap"] == 15.0  # 45 - 30
        assert "atteint l'objectif" in validation["recommendation"]
    
    def test_validate_target_resolution_failure(self):
        """Test de validation avec objectif non atteint."""
        report = MetricsReport(
            categorization_accuracy=70.0,
            resolution_rate=25.0,  # < 30%
            average_execution_time_ms=2000.0,
            template_coverage=60.0,
            category_breakdown={},
            timeout_rate=10.0,
            error_rate=20.0,
            total_puzzles_tested=100,
            successful_resolutions=25,
            method_transparency={},
            meets_target_resolution=False
        )
        
        validation = validate_target_resolution(report)
        
        assert validation["meets_target"] is False
        assert validation["status"] == "NEEDS_IMPROVEMENT"
        assert validation["gap"] == -5.0  # 25 - 30
        assert "Améliorer les algorithmes" in validation["recommendation"]
    
    def test_compare_metrics_over_time(self):
        """Test de comparaison de métriques dans le temps."""
        report1 = MetricsReport(
            categorization_accuracy=70.0,
            resolution_rate=25.0,
            average_execution_time_ms=2500.0,
            template_coverage=60.0,
            category_breakdown={},
            timeout_rate=15.0,
            error_rate=25.0,
            total_puzzles_tested=50,
            successful_resolutions=12,
            method_transparency={},
            meets_target_resolution=False
        )
        
        report2 = MetricsReport(
            categorization_accuracy=85.0,
            resolution_rate=40.0,
            average_execution_time_ms=2000.0,
            template_coverage=80.0,
            category_breakdown={},
            timeout_rate=10.0,
            error_rate=15.0,
            total_puzzles_tested=100,
            successful_resolutions=40,
            method_transparency={},
            meets_target_resolution=True
        )
        
        comparison = compare_metrics_over_time([report1, report2])
        
        assert comparison["resolution_rate_evolution"]["trend"] == "IMPROVEMENT"
        assert comparison["resolution_rate_evolution"]["change"] == 15.0  # 40 - 25
        assert comparison["execution_time_evolution"]["trend"] == "FASTER"
        assert comparison["execution_time_evolution"]["change"] == -500.0  # 2000 - 2500
        assert "calculs arithmétiques simples" in comparison["analysis_note"]
    
    def test_compare_metrics_insufficient_data(self):
        """Test de comparaison avec données insuffisantes."""
        report = MetricsReport(
            categorization_accuracy=80.0,
            resolution_rate=35.0,
            average_execution_time_ms=1500.0,
            template_coverage=70.0,
            category_breakdown={},
            timeout_rate=5.0,
            error_rate=10.0,
            total_puzzles_tested=50,
            successful_resolutions=17,
            method_transparency={},
            meets_target_resolution=True
        )
        
        comparison = compare_metrics_over_time([report])
        
        assert "error" in comparison
        assert "Au moins 2 rapports nécessaires" in comparison["error"]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
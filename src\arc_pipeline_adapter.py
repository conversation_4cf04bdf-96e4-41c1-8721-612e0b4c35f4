"""
Adaptateur pour intégrer le pipeline ARC-Solver avec les composants existants.

Ce module fait le pont entre le nouveau pipeline algorithmique et l'architecture
existante du projet (ArcDataset, CommandExecutor, GrammarTokenizer).
"""

import numpy as np
from typing import List, Dict, Any, Optional
import sys
import os

# Ajouter les chemins nécessaires
sys.path.append('.')
sys.path.append('src')

# Imports des composants existants
from arc_dataset import ArcDataset
from command_executor import CommandExecutor
from tokenizer import GrammarTokenizer
import config

# Imports des nouveaux composants du pipeline
from multi_example_dataset import MultiExampleARCDataset
from data_structures import PuzzleData, PipelineResult


class ARCPipelineAdapter:
    """
    Adaptateur pour intégrer le pipeline ARC-Solver avec les composants existants.
    
    Fait le pont entre:
    - MultiExampleDataset (nouveau) et ArcDataset (existant)
    - Pipeline algorithmique (nouveau) et CommandExecutor (existant)
    - Structures de données (nouvelles) et format existant
    """
    
    def __init__(self, data_dir: str = None):
        """
        Initialiser l'adaptateur.
        
        Args:
            data_dir: Répertoire des données ARC (utilise Config.DATA_DIR par défaut)
        """
        self.data_dir = data_dir or config.Config.DATA_DIR
        self.tokenizer = GrammarTokenizer()
        self.command_executor = CommandExecutor()
        
        # Initialiser les datasets
        self.arc_dataset = None
        self.multi_example_dataset = None
        
        self._initialize_datasets()
    
    def _initialize_datasets(self):
        """Initialiser les datasets existants et nouveaux."""
        try:
            # Dataset existant (pour compatibilité)
            if os.path.exists(self.data_dir):
                self.arc_dataset = ArcDataset(
                    data_folder=self.data_dir,
                    tokenizer=self.tokenizer,
                    max_prog_len=config.Config.MAX_PROG_LEN
                )
                print(f"✓ ArcDataset initialisé avec {len(self.arc_dataset)} puzzles")
            
            # Nouveau dataset multi-exemples
            if os.path.exists(self.data_dir):
                self.multi_example_dataset = MultiExampleARCDataset(
                    data_dir=self.data_dir,
                    max_grid_size=30
                )
                print(f"✓ MultiExampleDataset initialisé avec {len(self.multi_example_dataset)} puzzles")
                
        except Exception as e:
            print(f"⚠️  Erreur lors de l'initialisation des datasets: {e}")
    
    def convert_arc_to_puzzle_data(self, puzzle_index: int) -> Optional[PuzzleData]:
        """
        Convertir un puzzle du format ArcDataset vers PuzzleData.
        
        Args:
            puzzle_index: Index du puzzle dans le dataset
        
        Returns:
            PuzzleData ou None si erreur
        """
        if not self.multi_example_dataset or puzzle_index >= len(self.multi_example_dataset):
            return None
        
        try:
            # Récupérer les données du MultiExampleDataset
            puzzle_data = self.multi_example_dataset[puzzle_index]
            
            return PuzzleData(
                puzzle_id=f"arc_puzzle_{puzzle_index}",
                train_inputs=[grid.numpy() for grid in puzzle_data['train_inputs']],
                train_outputs=[grid.numpy() for grid in puzzle_data['train_outputs']],
                test_input=puzzle_data['test_input'].numpy(),
                expected_output=puzzle_data.get('expected_output')
            )
            
        except Exception as e:
            print(f"Erreur lors de la conversion du puzzle {puzzle_index}: {e}")
            return None
    
    def execute_pipeline_solution(self, solution_command: str, 
                                 input_grid: np.ndarray) -> Dict[str, Any]:
        """
        Exécuter une solution du pipeline via le CommandExecutor existant.
        
        Args:
            solution_command: Commande AGI générée par le pipeline
            input_grid: Grille d'entrée
        
        Returns:
            Résultat de l'exécution
        """
        try:
            # Préparer l'exécuteur avec la grille d'entrée
            self.command_executor.grid = input_grid.copy()
            self.command_executor.height, self.command_executor.width = input_grid.shape
            self.command_executor.error = None
            self.command_executor.history = []
            
            # Ajouter une commande INIT pour initialiser correctement la grille
            init_command = f"INIT {input_grid.shape[1]} {input_grid.shape[0]}"
            commands = [init_command] + self._parse_solution_commands(solution_command)
            
            # Exécuter via le CommandExecutor existant
            result = self.command_executor.execute_commands(commands)
            
            return {
                "success": result["success"],
                "output_grid": np.array(result["grid"]) if result["grid"] else None,
                "execution_time_ms": 0.0,  # Le CommandExecutor ne mesure pas le temps
                "error_message": result["error"],
                "commands_executed": result["history"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "output_grid": None,
                "execution_time_ms": 0.0,
                "error_message": f"Erreur d'exécution: {str(e)}",
                "commands_executed": []
            }
    
    def _parse_solution_commands(self, solution_command: str) -> List[str]:
        """
        Parser une commande de solution en liste de commandes individuelles.
        
        Args:
            solution_command: Commande complète
        
        Returns:
            Liste de commandes individuelles
        """
        if not solution_command:
            return []
        
        # Diviser par point-virgule ou nouvelle ligne
        commands = []
        for cmd in solution_command.replace('\n', ';').split(';'):
            cmd = cmd.strip()
            if cmd:
                commands.append(cmd)
        
        return commands
    
    def validate_solution_with_executor(self, solution_command: str,
                                      input_grid: np.ndarray,
                                      expected_output: np.ndarray) -> Dict[str, Any]:
        """
        Valider une solution en utilisant le CommandExecutor existant.
        
        Args:
            solution_command: Commande AGI à valider
            input_grid: Grille d'entrée
            expected_output: Sortie attendue
        
        Returns:
            Résultat de validation
        """
        try:
            # Exécuter la solution
            execution_result = self.execute_pipeline_solution(solution_command, input_grid)
            
            if not execution_result["success"]:
                return {
                    "valid": False,
                    "score": 0.0,
                    "error": execution_result["error_message"],
                    "output_matches": False
                }
            
            # Comparer avec la sortie attendue
            output_grid = execution_result["output_grid"]
            if output_grid is None:
                return {
                    "valid": False,
                    "score": 0.0,
                    "error": "Aucune grille de sortie générée",
                    "output_matches": False
                }
            
            # Vérifier les dimensions
            if output_grid.shape != expected_output.shape:
                return {
                    "valid": False,
                    "score": 0.0,
                    "error": f"Dimensions incorrectes: {output_grid.shape} vs {expected_output.shape}",
                    "output_matches": False
                }
            
            # Calculer le score de correspondance
            matches = np.sum(output_grid == expected_output)
            total_cells = expected_output.size
            score = matches / total_cells if total_cells > 0 else 0.0
            
            return {
                "valid": score == 1.0,
                "score": score,
                "error": None if score == 1.0 else f"Correspondance: {matches}/{total_cells} cellules",
                "output_matches": score == 1.0,
                "execution_details": execution_result
            }
            
        except Exception as e:
            return {
                "valid": False,
                "score": 0.0,
                "error": f"Erreur de validation: {str(e)}",
                "output_matches": False
            }
    
    def tokenize_solution(self, solution_command: str) -> List[int]:
        """
        Tokeniser une solution avec le GrammarTokenizer existant.
        
        Args:
            solution_command: Commande à tokeniser
        
        Returns:
            Liste de tokens
        """
        return self.tokenizer.tokenize(solution_command)
    
    def detokenize_solution(self, tokens: List[int]) -> str:
        """
        Détokeniser une solution avec le GrammarTokenizer existant.
        
        Args:
            tokens: Liste de tokens
        
        Returns:
            Commande détokenisée
        """
        return self.tokenizer.detokenize(tokens)
    
    def get_puzzle_by_id(self, puzzle_id: str) -> Optional[PuzzleData]:
        """
        Récupérer un puzzle par son ID depuis les datasets existants.
        
        Args:
            puzzle_id: Identifiant du puzzle
        
        Returns:
            PuzzleData ou None si non trouvé
        """
        # Pour l'instant, utiliser l'index comme ID
        try:
            index = int(puzzle_id.split('_')[-1]) if '_' in puzzle_id else int(puzzle_id)
            return self.convert_arc_to_puzzle_data(index)
        except (ValueError, IndexError):
            return None
    
    def get_available_puzzles(self) -> List[str]:
        """
        Obtenir la liste des puzzles disponibles.
        
        Returns:
            Liste des IDs de puzzles
        """
        if not self.multi_example_dataset:
            return []
        
        return [f"arc_puzzle_{i}" for i in range(len(self.multi_example_dataset))]
    
    def create_integration_report(self) -> Dict[str, Any]:
        """
        Créer un rapport d'intégration des composants.
        
        Returns:
            Rapport d'intégration
        """
        report = {
            "timestamp": __import__('time').time(),
            "components_status": {
                "arc_dataset": self.arc_dataset is not None,
                "multi_example_dataset": self.multi_example_dataset is not None,
                "tokenizer": self.tokenizer is not None,
                "command_executor": self.command_executor is not None
            },
            "dataset_sizes": {
                "arc_dataset": len(self.arc_dataset) if self.arc_dataset else 0,
                "multi_example_dataset": len(self.multi_example_dataset) if self.multi_example_dataset else 0
            },
            "tokenizer_vocab_size": len(self.tokenizer.vocab) if self.tokenizer else 0,
            "integration_features": [
                "Conversion ArcDataset -> PuzzleData",
                "Exécution via CommandExecutor existant",
                "Tokenisation via GrammarTokenizer existant",
                "Validation de solutions",
                "Accès unifié aux puzzles"
            ],
            "compatibility_notes": [
                "Compatible avec l'architecture existante",
                "Réutilise les composants validés",
                "Étend les fonctionnalités sans casser l'existant",
                "Permet migration progressive vers le nouveau pipeline"
            ]
        }
        
        return report


def test_adapter_integration():
    """
    Tester l'intégration de l'adaptateur avec les composants existants.
    """
    print("=== TEST D'INTÉGRATION DE L'ADAPTATEUR ===")
    
    try:
        # Initialiser l'adaptateur
        adapter = ARCPipelineAdapter()
        
        # Générer le rapport d'intégration
        report = adapter.create_integration_report()
        
        print(f"Statut des composants:")
        for component, status in report["components_status"].items():
            print(f"  • {component}: {'✅' if status else '❌'}")
        
        print(f"\nTailles des datasets:")
        for dataset, size in report["dataset_sizes"].items():
            print(f"  • {dataset}: {size} puzzles")
        
        print(f"\nTaille du vocabulaire: {report['tokenizer_vocab_size']} tokens")
        
        print(f"\nFonctionnalités d'intégration:")
        for feature in report["integration_features"]:
            print(f"  • {feature}")
        
        # Test de tokenisation
        test_command = "INIT 3 3; FILL 1 [0,0] [2,2]"
        tokens = adapter.tokenize_solution(test_command)
        detokenized = adapter.detokenize_solution(tokens)
        
        print(f"\nTest de tokenisation:")
        print(f"  • Commande originale: {test_command}")
        print(f"  • Tokens: {tokens[:10]}..." if len(tokens) > 10 else f"  • Tokens: {tokens}")
        print(f"  • Détokenisée: {detokenized}")
        
        # Test de conversion de puzzle (si des données sont disponibles)
        if adapter.multi_example_dataset and len(adapter.multi_example_dataset) > 0:
            puzzle = adapter.convert_arc_to_puzzle_data(0)
            if puzzle:
                print(f"\nTest de conversion de puzzle:")
                print(f"  • ID: {puzzle.puzzle_id}")
                print(f"  • Exemples d'entraînement: {len(puzzle.train_inputs)}")
                print(f"  • Taille grille test: {puzzle.test_input.shape}")
        
        print(f"\n✅ Intégration réussie - Tous les composants sont compatibles")
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur d'intégration: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_adapter_integration()
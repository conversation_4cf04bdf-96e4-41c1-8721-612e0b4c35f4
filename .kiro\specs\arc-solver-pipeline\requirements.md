# Requirements Document

## Introduction

Cette fonctionnalité implémente un pipeline de résolution ARC basé sur des algorithmes explicites et des calculs programmés, sans prétendre à des capacités d'IA inexistantes. Le système utilise une approche à deux étages : analyse de patterns par calculs géométriques, puis résolution de paramètres par recherche algorithmique.

## Requirements

### Requirement 1 - Dataset Multi-Exemples

**User Story:** En tant que développeur, je veux charger tous les exemples d'entraînement d'un puzzle ARC, afin de pouvoir analyser les patterns sur l'ensemble complet des données.

#### Acceptance Criteria

1. WHEN le système charge un puzzle THEN il SHALL retourner TOUS les exemples train (pas seulement le premier)
2. WHEN les grilles ont des tailles différentes THEN le système SHALL normaliser avec du padding
3. WHEN les données sont chargées THEN elles SHALL être converties en tenseurs PyTorch
4. IF un fichier JSON est corrompu THEN le système SHALL lever une exception explicite

### Requirement 2 - Analyseur de Patterns Géométriques

**User Story:** En tant que système, je veux détecter les transformations géométriques par calculs algorithmiques explicites, afin de catégoriser les puzzles sans prétendre à de l'apprentissage automatique.

#### Acceptance Criteria

1. WHEN le système compare deux grilles THEN il SHALL calculer les rotations par comparaison matricielle
2. WHEN le système analyse les symétries THEN il SHALL utiliser des calculs d'axes géométriques
3. WHEN le système détecte des patterns de couleur THEN il SHALL utiliser des histogrammes
4. WHEN le système trouve des motifs répétés THEN il SHALL utiliser des calculs de corrélation
5. IF une rotation est détectée THEN le puzzle SHALL être catégorisé comme "geometric_transform"
6. IF les couleurs changent THEN le puzzle SHALL être catégorisé comme "color_pattern"
7. IF la taille change THEN le puzzle SHALL être catégorisé comme "resize_operation"

### Requirement 3 - Générateur de Templates

**User Story:** En tant que système, je veux générer des templates de commandes AGI par substitution de variables, afin de créer des scénarios paramétrables sans intelligence artificielle.

#### Acceptance Criteria

1. WHEN le système reçoit une catégorie THEN il SHALL sélectionner le template correspondant depuis une base de données pré-définie
2. WHEN le système analyse les exemples THEN il SHALL identifier les valeurs variables (couleurs, coordonnées)
3. WHEN le système crée un template THEN il SHALL remplacer les valeurs par des placeholders
4. WHEN le système génère un template THEN il SHALL définir les contraintes de résolution
5. IF la catégorie est "color_fill" THEN le template SHALL être "FILL {color_var} [0,0 {width},{height}]"
6. IF la catégorie est "rotation" THEN le template SHALL être "ROTATE {angle} {center}"

### Requirement 4 - Résolveur de Paramètres

**User Story:** En tant que système, je veux résoudre les paramètres des templates par recherche algorithmique, afin de trouver les valeurs optimales sans apprentissage automatique.

#### Acceptance Criteria

1. WHEN le système reçoit un template THEN il SHALL énumérer toutes les valeurs possibles pour chaque variable
2. WHEN le système teste les combinaisons THEN il SHALL les valider sur tous les exemples d'entraînement
3. WHEN le système trouve plusieurs solutions THEN il SHALL sélectionner celle avec le meilleur score de validation
4. WHEN le système applique une solution THEN il SHALL l'exécuter sur l'input de test
5. IF aucune combinaison ne fonctionne THEN le système SHALL retourner une erreur explicite
6. IF la recherche dépasse 10 secondes THEN le système SHALL s'arrêter avec un timeout

### Requirement 5 - Pipeline d'Intégration

**User Story:** En tant qu'utilisateur, je veux exécuter le pipeline complet de résolution, afin d'obtenir une solution ARC par orchestration des composants algorithmiques.

#### Acceptance Criteria

1. WHEN le pipeline reçoit des données de puzzle THEN il SHALL analyser les patterns par calculs géométriques
2. WHEN l'analyse est terminée THEN il SHALL catégoriser le puzzle par règles conditionnelles
3. WHEN la catégorisation est faite THEN il SHALL générer un template par substitution de variables
4. WHEN le template est créé THEN il SHALL résoudre les paramètres par recherche algorithmique
5. WHEN les paramètres sont trouvés THEN il SHALL exécuter la solution via l'interpréteur AGI
6. IF une étape échoue THEN le pipeline SHALL s'arrêter et retourner l'erreur de l'étape

### Requirement 6 - Tests et Validation

**User Story:** En tant que développeur, je veux valider chaque composant individuellement et le pipeline complet, afin de garantir la qualité du code algorithmique.

#### Acceptance Criteria

1. WHEN les tests unitaires sont exécutés THEN chaque composant SHALL être testé individuellement
2. WHEN les tests d'intégration sont exécutés THEN le pipeline complet SHALL être testé sur des puzzles simples
3. WHEN les performances sont mesurées THEN les temps d'exécution SHALL être inférieurs à 10 secondes par puzzle
4. WHEN les métriques sont calculées THEN elles SHALL inclure la précision de catégorisation et le taux de résolution
5. IF un test échoue THEN le système SHALL fournir un message d'erreur détaillé

### Requirement 7 - Métriques et Transparence

**User Story:** En tant qu'utilisateur, je veux des métriques honnêtes sur les performances, afin de comprendre les capacités réelles du système algorithmique.

#### Acceptance Criteria

1. WHEN le système génère des métriques THEN il SHALL mesurer la précision de catégorisation en pourcentage
2. WHEN le système calcule les performances THEN il SHALL mesurer le taux de résolution des paramètres
3. WHEN le système rapporte les résultats THEN il SHALL indiquer le temps d'exécution algorithmique
4. WHEN le système présente les capacités THEN il SHALL préciser qu'il s'agit de calculs programmés, pas d'apprentissage
5. IF le système échoue sur un puzzle THEN il SHALL expliquer clairement pourquoi (template manquant, paramètres non trouvés, etc.)
6. WHEN le système atteint 30% de puzzles résolus THEN il SHALL être considéré comme fonctionnel